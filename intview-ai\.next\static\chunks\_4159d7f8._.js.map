{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Softwares/Ai%20bot/intview-ai/lib/utils.ts"], "sourcesContent": ["import { clsx, type ClassValue } from \"clsx\"\r\nimport { twMerge } from \"tailwind-merge\"\r\n\r\nexport function cn(...inputs: ClassValue[]) {\r\n  return twMerge(clsx(inputs))\r\n}\r\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,8JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,wIAAA,CAAA,OAAI,AAAD,EAAE;AACtB", "debugId": null}}, {"offset": {"line": 26, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Softwares/Ai%20bot/intview-ai/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\"\r\nimport { Slot } from \"@radix-ui/react-slot\"\r\nimport { cva, type VariantProps } from \"class-variance-authority\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nconst buttonVariants = cva(\r\n  \"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\r\n  {\r\n    variants: {\r\n      variant: {\r\n        default:\r\n          \"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90\",\r\n        destructive:\r\n          \"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\r\n        outline:\r\n          \"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50\",\r\n        secondary:\r\n          \"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80\",\r\n        ghost:\r\n          \"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50\",\r\n        link: \"text-primary underline-offset-4 hover:underline\",\r\n      },\r\n      size: {\r\n        default: \"h-9 px-4 py-2 has-[>svg]:px-3\",\r\n        sm: \"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5\",\r\n        lg: \"h-10 rounded-md px-6 has-[>svg]:px-4\",\r\n        icon: \"size-9\",\r\n      },\r\n    },\r\n    defaultVariants: {\r\n      variant: \"default\",\r\n      size: \"default\",\r\n    },\r\n  }\r\n)\r\n\r\nfunction Button({\r\n  className,\r\n  variant,\r\n  size,\r\n  asChild = false,\r\n  ...props\r\n}: React.ComponentProps<\"button\"> &\r\n  VariantProps<typeof buttonVariants> & {\r\n    asChild?: boolean\r\n  }) {\r\n  const Comp = asChild ? Slot : \"button\"\r\n\r\n  return (\r\n    <Comp\r\n      data-slot=\"button\"\r\n      className={cn(buttonVariants({ variant, size, className }))}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nexport { Button, buttonVariants }\r\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,iBAAiB,CAAA,GAAA,mKAAA,CAAA,MAAG,AAAD,EACvB,+bACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,aACE;YACF,SACE;YACF,WACE;YACF,OACE;YACF,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AAGF,SAAS,OAAO,EACd,SAAS,EACT,OAAO,EACP,IAAI,EACJ,UAAU,KAAK,EACf,GAAG,OAIF;IACD,MAAM,OAAO,UAAU,mKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACvD,GAAG,KAAK;;;;;;AAGf;KAnBS", "debugId": null}}, {"offset": {"line": 89, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Softwares/Ai%20bot/intview-ai/components/interview/InterviewInstructions.tsx"], "sourcesContent": ["\"use client\";\nimport { But<PERSON> } from \"@/components/ui/button\";\nimport { ArrowRight } from \"lucide-react\";\nimport React, { useState } from \"react\";\n\ntype InterviewInstructionsProps = {\n  candidateName?: string;\n  jobTitle?: string;\n  languages?: string[];\n  instructions?: string[];\n  environmentChecklist?: string[];\n  disclaimers?: string[];\n  onNext?: () => void;\n};\n\nconst defaultInstructions = [\n  \"The AI Video assessment consists of 5 questions and should take you 5 minutes to complete, depending on the number of questions you are assigned.\",\n  \"The AI Video assessment consists of 5 questions and should take you 5 minutes to complete, depending on the number of questions you are assigned.\",\n  \"The AI Video assessment consists of 5 questions and should take you 5 minutes to complete, depending on the number of questions you are assigned.\",\n  \"The AI Video assessment consists of 5 questions and should take you 5 minutes to complete, depending on the number of questions you are assigned.\",\n  \"The AI Video assessment consists of 5 questions and should take you 5 minutes to complete, depending on the number of questions you are assigned.\",\n];\n\nconst defaultEnvironment = [\n  \"To ensure an accurate assessment, please sit in a well lit and strong wifi area. Make sure your face is clearly visible, and avoid sitting with strong backlighting or in places where shadows may obscure your face.\",\n  \"To ensure an accurate assessment, please sit in a well lit and strong wifi area. Make sure your face is clearly visible, and avoid sitting with strong backlighting or in places where shadows may obscure your face.\",\n  \"To ensure an accurate assessment, please sit in a well lit and strong wifi area. Make sure your face is clearly visible, and avoid sitting with strong backlighting or in places where shadows may obscure your face.\",\n  \"To ensure an accurate assessment, please sit in a well lit and strong wifi area. Make sure your face is clearly visible, and avoid sitting with strong backlighting or in places where shadows may obscure your face.\",\n];\n\nconst defaultDisclaimers = [\n  \"Environment Requirements Ensure you are in a quiet, distraction-free space. Sit in a well-lit area so the avatar can see you clearly. Use a stable internet connection and a working camera & microphone .\",\n  \"AI Interview Format Your interviewer will be an AI avatar, speaking and listening in a natural, conversational style. You will respond to 5 preset questions, with roughly under 10 minutes total interview time. You may be gently prompted if your answers run long—please stay within the time suggested .\",\n  \"Recording & Usage This session will be fully recorded (audio & video) for review by our hiring team. Your responses and the recording will be processed by our AI scoring system to evaluate communication, problem-solving, and fit. All data is stored securely and used only for the purposes of hiring this role .\",\n  \"Independence & Integrity Please answer without external aids (notes, websites, or other people). If background noise or interruptions occur, you may be prompted to pause and restart your answer .\",\n];\n\nconst InterviewInstructions: React.FC<InterviewInstructionsProps> = ({\n  candidateName = \"Jonathan\",\n  jobTitle = \"Insurance Agent\",\n  languages = [\"English\", \"Chinese\"],\n  instructions = defaultInstructions,\n  environmentChecklist = defaultEnvironment,\n  disclaimers = defaultDisclaimers,\n  onNext,\n}) => {\n  const [isChecked, setIsChecked] = useState(false);\n\n  return (\n    <div className=\"flex-1 border border-gray-400 rounded-md h-fit bg-white\">\n      <div className=\"p-4 flex flex-col text-[#38383a]\">\n        <p className=\"font-semibold mb-8 text-xl\">\n          Instructions for Interview!\n        </p>\n        <div className=\"space-y-6\">\n          <div>\n            <p className=\" mb-2 text-md\">Hello {candidateName}!</p>\n            <p className=\"text-sm mb-4\">\n              As part of the process you are required to complete an AI video\n              assessment for the role of the {jobTitle}.\n            </p>\n          </div>\n\n          <div>\n            <p className=\"font-semibold mb-2 text-lg\">Interview Language</p>\n            <ul className=\"list-disc list-inside space-y-2 text-sm\">\n              {languages.map((language, index) => (\n                <li key={index}>{language}</li>\n              ))}\n            </ul>\n          </div>\n\n          <div>\n            <p className=\"font-semibold mb-2 text-lg\">Instructions</p>\n            <ul className=\"list-disc list-inside space-y-2 text-sm\">\n              {instructions.map((instruction, index) => (\n                <li key={index}>{instruction}</li>\n              ))}\n            </ul>\n          </div>\n\n          <div>\n            <p className=\"font-semibold mb-2 text-lg\">Environment Checklist:</p>\n            <ul className=\"list-disc list-inside space-y-2 text-sm\">\n              {environmentChecklist.map((item, index) => (\n                <li key={index}>{item}</li>\n              ))}\n            </ul>\n          </div>\n\n          <div>\n            <p className=\"font-semibold mb-2 text-lg\">Important Disclaimers:</p>\n            <ul className=\"list-disc list-inside space-y-2 text-sm\">\n              {disclaimers.map((disclaimer, index) => (\n                <li key={index}>{disclaimer}</li>\n              ))}\n            </ul>\n          </div>\n\n          <div className=\"flex items-start gap-2 mt-6\">\n            <input\n              type=\"checkbox\"\n              id=\"terms\"\n              checked={isChecked}\n              onChange={(e) => setIsChecked(e.target.checked)}\n              className=\"h-4 w-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500\"\n            />\n            <label htmlFor=\"terms\" className=\"text-[11px] text-[#38383a]\">\n              By checking this box, you agree with AI Interview{\" \"}\n              <span className=\"text-primary cursor-pointer font-medium\">\n                Terms of use\n              </span>\n              .\n            </label>\n          </div>\n          <div className=\"flex justify-center\">\n            <Button\n              disabled={!isChecked}\n              variant=\"default\"\n              size=\"lg\"\n              className=\"py-2 sm:py-6 text-sm sm:text-md rounded-full w-full sm:w-[200px] lg:w-[330px] flex items-center gap-2 group cursor-pointer text-white\"\n              onClick={() => onNext && onNext()}\n            >\n              Start Interview\n              <ArrowRight className=\"w-6 h-6 duration-300 group-hover:translate-x-1\" />\n            </Button>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default InterviewInstructions;\n"], "names": [], "mappings": ";;;;AACA;AACA;AACA;;;AAHA;;;;AAeA,MAAM,sBAAsB;IAC1B;IACA;IACA;IACA;IACA;CACD;AAED,MAAM,qBAAqB;IACzB;IACA;IACA;IACA;CACD;AAED,MAAM,qBAAqB;IACzB;IACA;IACA;IACA;CACD;AAED,MAAM,wBAA8D,CAAC,EACnE,gBAAgB,UAAU,EAC1B,WAAW,iBAAiB,EAC5B,YAAY;IAAC;IAAW;CAAU,EAClC,eAAe,mBAAmB,EAClC,uBAAuB,kBAAkB,EACzC,cAAc,kBAAkB,EAChC,MAAM,EACP;;IACC,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAE,WAAU;8BAA6B;;;;;;8BAG1C,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;;8CACC,6LAAC;oCAAE,WAAU;;wCAAgB;wCAAO;wCAAc;;;;;;;8CAClD,6LAAC;oCAAE,WAAU;;wCAAe;wCAEM;wCAAS;;;;;;;;;;;;;sCAI7C,6LAAC;;8CACC,6LAAC;oCAAE,WAAU;8CAA6B;;;;;;8CAC1C,6LAAC;oCAAG,WAAU;8CACX,UAAU,GAAG,CAAC,CAAC,UAAU,sBACxB,6LAAC;sDAAgB;2CAAR;;;;;;;;;;;;;;;;sCAKf,6LAAC;;8CACC,6LAAC;oCAAE,WAAU;8CAA6B;;;;;;8CAC1C,6LAAC;oCAAG,WAAU;8CACX,aAAa,GAAG,CAAC,CAAC,aAAa,sBAC9B,6LAAC;sDAAgB;2CAAR;;;;;;;;;;;;;;;;sCAKf,6LAAC;;8CACC,6LAAC;oCAAE,WAAU;8CAA6B;;;;;;8CAC1C,6LAAC;oCAAG,WAAU;8CACX,qBAAqB,GAAG,CAAC,CAAC,MAAM,sBAC/B,6LAAC;sDAAgB;2CAAR;;;;;;;;;;;;;;;;sCAKf,6LAAC;;8CACC,6LAAC;oCAAE,WAAU;8CAA6B;;;;;;8CAC1C,6LAAC;oCAAG,WAAU;8CACX,YAAY,GAAG,CAAC,CAAC,YAAY,sBAC5B,6LAAC;sDAAgB;2CAAR;;;;;;;;;;;;;;;;sCAKf,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCACC,MAAK;oCACL,IAAG;oCACH,SAAS;oCACT,UAAU,CAAC,IAAM,aAAa,EAAE,MAAM,CAAC,OAAO;oCAC9C,WAAU;;;;;;8CAEZ,6LAAC;oCAAM,SAAQ;oCAAQ,WAAU;;wCAA6B;wCACV;sDAClD,6LAAC;4CAAK,WAAU;sDAA0C;;;;;;wCAEnD;;;;;;;;;;;;;sCAIX,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC,8HAAA,CAAA,SAAM;gCACL,UAAU,CAAC;gCACX,SAAQ;gCACR,MAAK;gCACL,WAAU;gCACV,SAAS,IAAM,UAAU;;oCAC1B;kDAEC,6LAAC,qNAAA,CAAA,aAAU;wCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOpC;GA9FM;KAAA;uCAgGS", "debugId": null}}, {"offset": {"line": 396, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Softwares/Ai%20bot/intview-ai/components/JobInfoCard.tsx"], "sourcesContent": ["import { MapPin, BriefcaseBusiness } from \"lucide-react\";\r\n\r\nconst JobInfoCard = () => {\r\n  return (\r\n    <div className=\"bg-white p-4 rounded-2xl shadow-sm mb-6 max-w-xl\">\r\n      <div className=\"flex justify-between items-start\">\r\n        <div>\r\n          <h2 className=\"text-xl font-semibold mb-3\">\r\n            UX/UI Designer for Ai-Interview Web App\r\n          </h2>\r\n          <div className=\"flex gap-2 leading-relaxed mb-3 flex-wrap\">\r\n            <p className=\"text-sm text-gray-600 font-medium\">\r\n              $500 - $1000 <span className=\"font-extrabold px-1\">·</span>\r\n            </p>\r\n            <div className=\"flex gap-1 items-center\">\r\n              <MapPin className=\"w-4 h-5\" />\r\n              <p className=\"text-sm text-gray-600 font-medium\">New York</p>\r\n            </div>\r\n            <div className=\"flex gap-1 items-center\">\r\n              <BriefcaseBusiness className=\"w-4 h-5\" />\r\n              <p className=\"text-sm text-gray-600 font-medium\">\r\n                Onsite / Remote\r\n              </p>\r\n            </div>\r\n          </div>\r\n          <p className=\"text-sm text-gray-500 mt-1\">\r\n            We&apos;re building an AI-powered interview tool. We expect you to\r\n            help users prepare by giving human interview experience generation.\r\n          </p>\r\n        </div>\r\n        <span className=\"text-xs bg-[#CCFFB1] text-green-700 px-3 py-1 rounded-full font-medium\">\r\n          Active\r\n        </span>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default JobInfoCard;\r\n"], "names": [], "mappings": ";;;;AAAA;AAAA;;;AAEA,MAAM,cAAc;IAClB,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC;;sCACC,6LAAC;4BAAG,WAAU;sCAA6B;;;;;;sCAG3C,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAE,WAAU;;wCAAoC;sDAClC,6LAAC;4CAAK,WAAU;sDAAsB;;;;;;;;;;;;8CAErD,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,6MAAA,CAAA,SAAM;4CAAC,WAAU;;;;;;sDAClB,6LAAC;4CAAE,WAAU;sDAAoC;;;;;;;;;;;;8CAEnD,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,mOAAA,CAAA,oBAAiB;4CAAC,WAAU;;;;;;sDAC7B,6LAAC;4CAAE,WAAU;sDAAoC;;;;;;;;;;;;;;;;;;sCAKrD,6LAAC;4BAAE,WAAU;sCAA6B;;;;;;;;;;;;8BAK5C,6LAAC;oBAAK,WAAU;8BAAyE;;;;;;;;;;;;;;;;;AAMjG;KAlCM;uCAoCS", "debugId": null}}, {"offset": {"line": 542, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Softwares/Ai%20bot/intview-ai/components/QuestionsList.tsx"], "sourcesContent": ["type QuestionsListProps = {\r\n  currentQuestion?: number;\r\n  className?: string;\r\n};\r\n\r\nconst QuestionsList = ({\r\n  currentQuestion = 1,\r\n  className,\r\n}: QuestionsListProps) => {\r\n  const questions = [\r\n    \"Tell us about yourself?\",\r\n    \"What are your strengths?\",\r\n    \"Why do you want this job?\",\r\n    \"Where do you see yourself in 5 years?\",\r\n  ];\r\n\r\n  return (\r\n    <div\r\n      className={`rounded-2xl bg-white p-4 w-full max-w-[300px] sm:w-[300px] h-[488px] shadow-sm overflow-y-auto scrollbar-hidden ${\r\n        className || \"\"\r\n      }`}\r\n    >\r\n      {\" \"}\r\n      <h3 className=\"font-semibold text-lg mb-6\">Questions</h3>\r\n      <ul className=\"relative space-y-8  \">\r\n        {Array.from({ length: 4 }, (_, i) => (\r\n          <li\r\n            key={i}\r\n            className=\"relative flex items-start space-x-3 mt-4 mb-0 sm:mb-5\"\r\n          >\r\n            {i !== 3 && (\r\n              <span className=\"absolute left-[17px] pl-[3px] top-6 mt-11 h-10 w-[3px] rounded-full bg-gradient-to-b from-white to-[#6938EF]\" />\r\n            )}\r\n            <div\r\n              className={`rounded-full w-7 h-7 mt-7 flex items-center p-5 justify-center text-sm font-medium z-10 ${\r\n                i + 1 === currentQuestion\r\n                  ? \"bg-[#6938EF] text-white\"\r\n                  : i + 1 < currentQuestion\r\n                    ? \"bg-green-500 text-white\"\r\n                    : \"bg-[#C7ACF5] text-white\"\r\n              }`}\r\n            >\r\n              {i + 1 < currentQuestion ? \"✓\" : i + 1}\r\n            </div>\r\n            <span\r\n              className={`text-md font-medium mt-7 ${\r\n                i + 1 === currentQuestion\r\n                  ? \"text-[#6938EF] font-semibold\"\r\n                  : \"text-[#616161]\"\r\n              }`}\r\n            >\r\n              {questions[i]}\r\n            </span>\r\n          </li>\r\n        ))}\r\n      </ul>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default QuestionsList;\r\n"], "names": [], "mappings": ";;;;;AAKA,MAAM,gBAAgB,CAAC,EACrB,kBAAkB,CAAC,EACnB,SAAS,EACU;IACnB,MAAM,YAAY;QAChB;QACA;QACA;QACA;KACD;IAED,qBACE,6LAAC;QACC,WAAW,CAAC,gHAAgH,EAC1H,aAAa,IACb;;YAED;0BACD,6LAAC;gBAAG,WAAU;0BAA6B;;;;;;0BAC3C,6LAAC;gBAAG,WAAU;0BACX,MAAM,IAAI,CAAC;oBAAE,QAAQ;gBAAE,GAAG,CAAC,GAAG,kBAC7B,6LAAC;wBAEC,WAAU;;4BAET,MAAM,mBACL,6LAAC;gCAAK,WAAU;;;;;;0CAElB,6LAAC;gCACC,WAAW,CAAC,wFAAwF,EAClG,IAAI,MAAM,kBACN,4BACA,IAAI,IAAI,kBACN,4BACA,2BACN;0CAED,IAAI,IAAI,kBAAkB,MAAM,IAAI;;;;;;0CAEvC,6LAAC;gCACC,WAAW,CAAC,yBAAyB,EACnC,IAAI,MAAM,kBACN,iCACA,kBACJ;0CAED,SAAS,CAAC,EAAE;;;;;;;uBAxBV;;;;;;;;;;;;;;;;AA+BjB;KArDM;uCAuDS", "debugId": null}}, {"offset": {"line": 632, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Softwares/Ai%20bot/intview-ai/public/images/avator.png.mjs%20%28structured%20image%20object%29"], "sourcesContent": ["import src from \"IMAGE\";\nexport default { src, width: 363, height: 663, blurDataURL: \"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAQAAAAICAYAAADeM14FAAAAk0lEQVR42gGIAHf/AIJ+cP6DgXL/oqaV/46Xdv4AfXhp/3hwZf+IiHb/j5J3/wB0c27/cmdh/399ef+oppj/AExbeP96gpf/UVtv/3l/if8ALDhQ/3qDlf9ka3r/bnB8/wBJU2v/jpWm/7m8xv/Evrz/AFhUW/+in6v/193o/9PZ4/8AYWRs/svT4P/a4+//ytbn/hi3VG7o7wRAAAAAAElFTkSuQmCC\", blurWidth: 4, blurHeight: 8 }\n"], "names": [], "mappings": ";;;AAAA;;uCACe;IAAE,KAAA,mHAAA,CAAA,UAAG;IAAE,OAAO;IAAK,QAAQ;IAAK,aAAa;IAA0S,WAAW;IAAG,YAAY;AAAE", "debugId": null}}, {"offset": {"line": 654, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Softwares/Ai%20bot/intview-ai/components/CandidateImage.tsx"], "sourcesContent": ["import Image from \"next/image\";\r\nimport AvatarImage from \"@/public/images/avator.png\";\r\n\r\ntype CandidateImageProps = {\r\n  className?: string;\r\n};\r\n\r\nconst CandidateImage = ({ className }: CandidateImageProps) => {\r\n  return (\r\n    <div className=\"mt-6 md:mt-0\">\r\n      <Image\r\n        src={AvatarImage}\r\n        alt=\"Interviewee\"\r\n        className={`rounded-lg object-cover ${className}`}\r\n        width={300}\r\n        height={300}\r\n      />\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default CandidateImage;\r\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;;AAMA,MAAM,iBAAiB,CAAC,EAAE,SAAS,EAAuB;IACxD,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC,gIAAA,CAAA,UAAK;YACJ,KAAK,uRAAA,CAAA,UAAW;YAChB,KAAI;YACJ,WAAW,CAAC,wBAAwB,EAAE,WAAW;YACjD,OAAO;YACP,QAAQ;;;;;;;;;;;AAIhB;KAZM;uCAcS", "debugId": null}}, {"offset": {"line": 696, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Softwares/Ai%20bot/intview-ai/components/InterviewLayout.tsx"], "sourcesContent": ["import { ReactNode } from \"react\";\r\n\r\nconst InterviewLayout = ({ children }: { children: ReactNode }) => {\r\n  return (\r\n    <div className=\"border rounded-lg p-6 min-h-[600px] mb-4 flex-1\">\r\n      {children}\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default InterviewLayout;\r\n"], "names": [], "mappings": ";;;;;AAEA,MAAM,kBAAkB,CAAC,EAAE,QAAQ,EAA2B;IAC5D,qBACE,6LAAC;QAAI,WAAU;kBACZ;;;;;;AAGP;KANM;uCAQS", "debugId": null}}, {"offset": {"line": 724, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Softwares/Ai%20bot/intview-ai/components/interview/QuestionsPage.tsx"], "sourcesContent": ["\"use client\";\nimport { ArrowR<PERSON> } from \"lucide-react\";\nimport JobInfoCard from \"@/components/JobInfoCard\";\nimport QuestionsList from \"@/components/QuestionsList\";\nimport CandidateImage from \"@/components/CandidateImage\";\nimport InterviewLayout from \"@/components/InterviewLayout\";\nimport { Button } from \"@/components/ui/button\";\n\ntype QuestionsPageProps = {\n  onNext?: () => void;\n};\n\nconst QuestionsPage = ({ onNext }: QuestionsPageProps) => {\n  return (\n    <div className=\"h-screen\">\n      <JobInfoCard />\n\n      <InterviewLayout>\n        <div className=\"flex flex-col lg:flex-row gap-6 lg:gap-10 justify-center items-center lg:items-start\">\n          <QuestionsList className=\"h-[550px]\" />\n          <CandidateImage />\n        </div>\n\n        <div className=\"flex justify-center mt-10 gap-4\">\n          <Button\n            variant=\"default\"\n            size=\"lg\"\n            className=\"py-2 sm:py-6 text-sm sm:text-md rounded-full w-full sm:w-[200px] lg:w-[330px] flex items-center gap-2 group cursor-pointer text-white\"\n            onClick={() => onNext && onNext()}\n          >\n            Start Interview\n            <ArrowRight className=\"w-6 h-6 duration-300 group-hover:translate-x-1\" />\n          </Button>\n        </div>\n      </InterviewLayout>\n    </div>\n  );\n};\n\nexport default QuestionsPage;\n"], "names": [], "mappings": ";;;;AACA;AACA;AACA;AACA;AACA;AACA;AANA;;;;;;;;AAYA,MAAM,gBAAgB,CAAC,EAAE,MAAM,EAAsB;IACnD,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC,6HAAA,CAAA,UAAW;;;;;0BAEZ,6LAAC,iIAAA,CAAA,UAAe;;kCACd,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,+HAAA,CAAA,UAAa;gCAAC,WAAU;;;;;;0CACzB,6LAAC,gIAAA,CAAA,UAAc;;;;;;;;;;;kCAGjB,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC,8HAAA,CAAA,SAAM;4BACL,SAAQ;4BACR,MAAK;4BACL,WAAU;4BACV,SAAS,IAAM,UAAU;;gCAC1B;8CAEC,6LAAC,qNAAA,CAAA,aAAU;oCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAMlC;KAzBM;uCA2BS", "debugId": null}}, {"offset": {"line": 827, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Softwares/Ai%20bot/intview-ai/types/interview.ts"], "sourcesContent": ["// Types for Sequential Interview System\n\nexport interface InterviewQuestion {\n  id: string;\n  text: string;\n  order: number;\n  category?: string;\n  expectedDuration?: number; // in seconds\n}\n\nexport interface QuestionResponse {\n  questionId: string;\n  userAnswer: string;\n  timestamp: Date;\n  duration?: number; // time taken to answer in seconds\n  videoUrl?: string; // D-ID generated video URL for the question\n}\n\nexport interface InterviewSession {\n  id: string;\n  startTime: Date;\n  endTime?: Date;\n  currentQuestionIndex: number;\n  questions: InterviewQuestion[];\n  responses: QuestionResponse[];\n  status: 'not_started' | 'in_progress' | 'completed' | 'paused';\n  candidateId?: string;\n  jobId?: string;\n}\n\nexport interface SequentialInterviewState {\n  session: InterviewSession | null;\n  currentQuestion: InterviewQuestion | null;\n  currentVideoUrl: string | null;\n  isGeneratingVideo: boolean;\n  isSubmitting: boolean;\n  userAnswer: string;\n  error: string | null;\n}\n\nexport interface SequentialInterviewActions {\n  startInterview: (questions: InterviewQuestion[]) => void;\n  submitAnswer: (answer: string) => Promise<void>;\n  nextQuestion: () => Promise<void>;\n  pauseInterview: () => void;\n  resumeInterview: () => void;\n  completeInterview: () => void;\n  resetInterview: () => void;\n  setUserAnswer: (answer: string) => void;\n  clearError: () => void;\n}\n\n// Default interview questions\nexport const DEFAULT_INTERVIEW_QUESTIONS: InterviewQuestion[] = [\n  {\n    id: 'q1',\n    text: 'Tell us about yourself and your background.',\n    order: 1,\n    category: 'introduction',\n    expectedDuration: 120,\n  },\n  {\n    id: 'q2',\n    text: 'What are your key strengths and how do they relate to this position?',\n    order: 2,\n    category: 'strengths',\n    expectedDuration: 90,\n  },\n  {\n    id: 'q3',\n    text: 'Why do you want to work for our company?',\n    order: 3,\n    category: 'motivation',\n    expectedDuration: 90,\n  },\n  {\n    id: 'q4',\n    text: 'Where do you see yourself in 5 years?',\n    order: 4,\n    category: 'career_goals',\n    expectedDuration: 90,\n  },\n];\n\n// Configuration for D-ID integration\nexport interface DIDConfig {\n  apiKey: string;\n  sourceUrl?: string;\n  voiceId?: string;\n  provider?: string;\n}\n\n// Error types\nexport class InterviewError extends Error {\n  constructor(\n    message: string,\n    public code?: string,\n    public details?: any\n  ) {\n    super(message);\n    this.name = 'InterviewError';\n  }\n}\n\nexport class DIDIntegrationError extends InterviewError {\n  constructor(message: string, details?: any) {\n    super(message, 'DID_INTEGRATION_ERROR', details);\n    this.name = 'DIDIntegrationError';\n  }\n}\n"], "names": [], "mappings": "AAAA,wCAAwC;;;;;;AAqDjC,MAAM,8BAAmD;IAC9D;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,UAAU;QACV,kBAAkB;IACpB;IACA;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,UAAU;QACV,kBAAkB;IACpB;IACA;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,UAAU;QACV,kBAAkB;IACpB;IACA;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,UAAU;QACV,kBAAkB;IACpB;CACD;AAWM,MAAM,uBAAuB;;;IAClC,YACE,OAAe,EACf,AAAO,IAAa,EACpB,AAAO,OAAa,CACpB;QACA,KAAK,CAAC,eAHC,OAAA,WACA,UAAA;QAGP,IAAI,CAAC,IAAI,GAAG;IACd;AACF;AAEO,MAAM,4BAA4B;IACvC,YAAY,OAAe,EAAE,OAAa,CAAE;QAC1C,KAAK,CAAC,SAAS,yBAAyB;QACxC,IAAI,CAAC,IAAI,GAAG;IACd;AACF", "debugId": null}}, {"offset": {"line": 886, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Softwares/Ai%20bot/intview-ai/lib/did-api.ts"], "sourcesContent": ["// D-ID API Integration Service\n// Based on patterns from AI speaking bot implementation\n\nexport interface DIDTalkRequest {\n  script: {\n    type: 'text';\n    input: string;\n    voice_id?: string;\n    provider?: string;\n    style?: string;\n  };\n  source_url?: string;\n  config?: {\n    fluent?: boolean;\n    pad_audio?: number;\n    driver_expressions?: {\n      expressions?: Array<{\n        start_frame: number;\n        expression: string;\n        intensity: number;\n      }>;\n    };\n  };\n}\n\nexport interface DIDTalkResponse {\n  id: string;\n  status: 'created' | 'started' | 'done' | 'error';\n  result_url?: string;\n  error?: {\n    kind: string;\n    description: string;\n  };\n  metadata?: {\n    driver_url?: string;\n    mouth_open?: string;\n    source_url?: string;\n  };\n}\n\nexport interface DIDApiError {\n  message: string;\n  status?: number;\n  details?: string;\n}\n\nclass DIDApiService {\n  private readonly apiUrl = 'https://api.d-id.com/talks';\n  private readonly apiKey: string;\n\n  constructor(apiKey: string) {\n    this.apiKey = apiKey;\n  }\n\n  /**\n   * Create a new talk (video generation) request\n   */\n  async createTalk(request: DIDTalkRequest): Promise<DIDTalkResponse> {\n    try {\n      const response = await fetch(this.apiUrl, {\n        method: 'POST',\n        headers: {\n          'Authorization': `Basic ${btoa(this.apiKey)}`,\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify(request),\n      });\n\n      if (!response.ok) {\n        const errorText = await response.text();\n        throw new Error(`Failed to create talk: ${response.status} ${response.statusText} - ${errorText}`);\n      }\n\n      const data: DIDTalkResponse = await response.json();\n      return data;\n    } catch (error) {\n      console.error('Error creating D-ID talk:', error);\n      throw new DIDApiError({\n        message: error instanceof Error ? error.message : 'Unknown error creating talk',\n        status: error instanceof Error && 'status' in error ? (error as any).status : undefined,\n      });\n    }\n  }\n\n  /**\n   * Get the status of a talk by ID\n   */\n  async getTalkStatus(talkId: string): Promise<DIDTalkResponse> {\n    try {\n      const response = await fetch(`${this.apiUrl}/${talkId}`, {\n        headers: {\n          'Authorization': `Basic ${btoa(this.apiKey)}`,\n          'Content-Type': 'application/json',\n        },\n      });\n\n      if (!response.ok) {\n        const errorText = await response.text();\n        throw new Error(`Failed to get talk status: ${response.status} ${response.statusText} - ${errorText}`);\n      }\n\n      const data: DIDTalkResponse = await response.json();\n      return data;\n    } catch (error) {\n      console.error('Error getting D-ID talk status:', error);\n      throw new DIDApiError({\n        message: error instanceof Error ? error.message : 'Unknown error getting talk status',\n        status: error instanceof Error && 'status' in error ? (error as any).status : undefined,\n      });\n    }\n  }\n\n  /**\n   * Poll for talk completion with timeout\n   */\n  async waitForTalkCompletion(\n    talkId: string,\n    options: {\n      pollInterval?: number;\n      maxAttempts?: number;\n    } = {}\n  ): Promise<DIDTalkResponse> {\n    const { pollInterval = 3000, maxAttempts = 20 } = options;\n    let attempts = 0;\n\n    while (attempts < maxAttempts) {\n      try {\n        const status = await this.getTalkStatus(talkId);\n        \n        if (status.status === 'done' && status.result_url) {\n          return status;\n        }\n        \n        if (status.status === 'error') {\n          throw new DIDApiError({\n            message: `Talk generation failed: ${status.error?.description || 'Unknown error'}`,\n            details: status.error?.kind,\n          });\n        }\n\n        // Wait before next poll\n        await new Promise(resolve => setTimeout(resolve, pollInterval));\n        attempts++;\n      } catch (error) {\n        if (error instanceof DIDApiError) {\n          throw error;\n        }\n        console.error('Error polling talk status:', error);\n        attempts++;\n        if (attempts >= maxAttempts) {\n          throw new DIDApiError({\n            message: 'Timeout waiting for talk completion',\n            details: `Failed after ${maxAttempts} attempts`,\n          });\n        }\n        await new Promise(resolve => setTimeout(resolve, pollInterval));\n      }\n    }\n\n    throw new DIDApiError({\n      message: 'Timeout waiting for talk completion',\n      details: `No response after ${maxAttempts} attempts`,\n    });\n  }\n\n  /**\n   * Generate a complete video with question text\n   */\n  async generateQuestionVideo(\n    questionText: string,\n    options: {\n      sourceUrl?: string;\n      voiceId?: string;\n      provider?: string;\n    } = {}\n  ): Promise<string> {\n    try {\n      // Create the talk request\n      const talkRequest: DIDTalkRequest = {\n        script: {\n          type: 'text',\n          input: questionText,\n          voice_id: options.voiceId,\n          provider: options.provider,\n        },\n        source_url: options.sourceUrl,\n        config: {\n          fluent: true,\n          pad_audio: 0.5,\n        },\n      };\n\n      // Create the talk\n      const createResponse = await this.createTalk(talkRequest);\n      \n      // Wait for completion\n      const completedTalk = await this.waitForTalkCompletion(createResponse.id);\n      \n      if (!completedTalk.result_url) {\n        throw new DIDApiError({\n          message: 'No video URL returned from completed talk',\n        });\n      }\n\n      return completedTalk.result_url;\n    } catch (error) {\n      console.error('Error generating question video:', error);\n      throw error instanceof DIDApiError ? error : new DIDApiError({\n        message: error instanceof Error ? error.message : 'Unknown error generating video',\n      });\n    }\n  }\n}\n\n// Export a factory function to create the service\nexport function createDIDApiService(apiKey: string): DIDApiService {\n  return new DIDApiService(apiKey);\n}\n\n// Export the service class for direct instantiation if needed\nexport { DIDApiService };\n"], "names": [], "mappings": "AAAA,+BAA+B;AAC/B,wDAAwD;;;;;AA6CxD,MAAM;IACa,SAAS,6BAA6B;IACtC,OAAe;IAEhC,YAAY,MAAc,CAAE;QAC1B,IAAI,CAAC,MAAM,GAAG;IAChB;IAEA;;GAEC,GACD,MAAM,WAAW,OAAuB,EAA4B;QAClE,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,IAAI,CAAC,MAAM,EAAE;gBACxC,QAAQ;gBACR,SAAS;oBACP,iBAAiB,CAAC,MAAM,EAAE,KAAK,IAAI,CAAC,MAAM,GAAG;oBAC7C,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;YACvB;YAEA,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,YAAY,MAAM,SAAS,IAAI;gBACrC,MAAM,IAAI,MAAM,CAAC,uBAAuB,EAAE,SAAS,MAAM,CAAC,CAAC,EAAE,SAAS,UAAU,CAAC,GAAG,EAAE,WAAW;YACnG;YAEA,MAAM,OAAwB,MAAM,SAAS,IAAI;YACjD,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,6BAA6B;YAC3C,MAAM,IAAI,YAAY;gBACpB,SAAS,iBAAiB,QAAQ,MAAM,OAAO,GAAG;gBAClD,QAAQ,iBAAiB,SAAS,YAAY,QAAQ,AAAC,MAAc,MAAM,GAAG;YAChF;QACF;IACF;IAEA;;GAEC,GACD,MAAM,cAAc,MAAc,EAA4B;QAC5D,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC,CAAC,EAAE,QAAQ,EAAE;gBACvD,SAAS;oBACP,iBAAiB,CAAC,MAAM,EAAE,KAAK,IAAI,CAAC,MAAM,GAAG;oBAC7C,gBAAgB;gBAClB;YACF;YAEA,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,YAAY,MAAM,SAAS,IAAI;gBACrC,MAAM,IAAI,MAAM,CAAC,2BAA2B,EAAE,SAAS,MAAM,CAAC,CAAC,EAAE,SAAS,UAAU,CAAC,GAAG,EAAE,WAAW;YACvG;YAEA,MAAM,OAAwB,MAAM,SAAS,IAAI;YACjD,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,mCAAmC;YACjD,MAAM,IAAI,YAAY;gBACpB,SAAS,iBAAiB,QAAQ,MAAM,OAAO,GAAG;gBAClD,QAAQ,iBAAiB,SAAS,YAAY,QAAQ,AAAC,MAAc,MAAM,GAAG;YAChF;QACF;IACF;IAEA;;GAEC,GACD,MAAM,sBACJ,MAAc,EACd,UAGI,CAAC,CAAC,EACoB;QAC1B,MAAM,EAAE,eAAe,IAAI,EAAE,cAAc,EAAE,EAAE,GAAG;QAClD,IAAI,WAAW;QAEf,MAAO,WAAW,YAAa;YAC7B,IAAI;gBACF,MAAM,SAAS,MAAM,IAAI,CAAC,aAAa,CAAC;gBAExC,IAAI,OAAO,MAAM,KAAK,UAAU,OAAO,UAAU,EAAE;oBACjD,OAAO;gBACT;gBAEA,IAAI,OAAO,MAAM,KAAK,SAAS;oBAC7B,MAAM,IAAI,YAAY;wBACpB,SAAS,CAAC,wBAAwB,EAAE,OAAO,KAAK,EAAE,eAAe,iBAAiB;wBAClF,SAAS,OAAO,KAAK,EAAE;oBACzB;gBACF;gBAEA,wBAAwB;gBACxB,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;gBACjD;YACF,EAAE,OAAO,OAAO;gBACd,IAAI,iBAAiB,aAAa;oBAChC,MAAM;gBACR;gBACA,QAAQ,KAAK,CAAC,8BAA8B;gBAC5C;gBACA,IAAI,YAAY,aAAa;oBAC3B,MAAM,IAAI,YAAY;wBACpB,SAAS;wBACT,SAAS,CAAC,aAAa,EAAE,YAAY,SAAS,CAAC;oBACjD;gBACF;gBACA,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;YACnD;QACF;QAEA,MAAM,IAAI,YAAY;YACpB,SAAS;YACT,SAAS,CAAC,kBAAkB,EAAE,YAAY,SAAS,CAAC;QACtD;IACF;IAEA;;GAEC,GACD,MAAM,sBACJ,YAAoB,EACpB,UAII,CAAC,CAAC,EACW;QACjB,IAAI;YACF,0BAA0B;YAC1B,MAAM,cAA8B;gBAClC,QAAQ;oBACN,MAAM;oBACN,OAAO;oBACP,UAAU,QAAQ,OAAO;oBACzB,UAAU,QAAQ,QAAQ;gBAC5B;gBACA,YAAY,QAAQ,SAAS;gBAC7B,QAAQ;oBACN,QAAQ;oBACR,WAAW;gBACb;YACF;YAEA,kBAAkB;YAClB,MAAM,iBAAiB,MAAM,IAAI,CAAC,UAAU,CAAC;YAE7C,sBAAsB;YACtB,MAAM,gBAAgB,MAAM,IAAI,CAAC,qBAAqB,CAAC,eAAe,EAAE;YAExE,IAAI,CAAC,cAAc,UAAU,EAAE;gBAC7B,MAAM,IAAI,YAAY;oBACpB,SAAS;gBACX;YACF;YAEA,OAAO,cAAc,UAAU;QACjC,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,oCAAoC;YAClD,MAAM,iBAAiB,cAAc,QAAQ,IAAI,YAAY;gBAC3D,SAAS,iBAAiB,QAAQ,MAAM,OAAO,GAAG;YACpD;QACF;IACF;AACF;AAGO,SAAS,oBAAoB,MAAc;IAChD,OAAO,IAAI,cAAc;AAC3B", "debugId": null}}, {"offset": {"line": 1037, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Softwares/Ai%20bot/intview-ai/hooks/useSequentialInterview.ts"], "sourcesContent": ["'use client';\n\nimport { useState, useCallback, useEffect } from 'react';\nimport { \n  InterviewQuestion, \n  QuestionResponse, \n  InterviewSession, \n  SequentialInterviewState, \n  SequentialInterviewActions,\n  DEFAULT_INTERVIEW_QUESTIONS,\n  DIDConfig,\n  InterviewError,\n  DIDIntegrationError\n} from '@/types/interview';\nimport { createDIDApiService } from '@/lib/did-api';\n\ninterface UseSequentialInterviewOptions {\n  didConfig?: DIDConfig;\n  questions?: InterviewQuestion[];\n  autoGenerateVideos?: boolean;\n}\n\nexport function useSequentialInterview(options: UseSequentialInterviewOptions = {}) {\n  const {\n    didConfig,\n    questions = DEFAULT_INTERVIEW_QUESTIONS,\n    autoGenerateVideos = true\n  } = options;\n\n  // State\n  const [state, setState] = useState<SequentialInterviewState>({\n    session: null,\n    currentQuestion: null,\n    currentVideoUrl: null,\n    isGeneratingVideo: false,\n    isSubmitting: false,\n    userAnswer: '',\n    error: null,\n  });\n\n  // D-ID service instance\n  const didService = didConfig ? createDIDApiService(didConfig.apiKey) : null;\n\n  // Helper function to update state\n  const updateState = useCallback((updates: Partial<SequentialInterviewState>) => {\n    setState(prev => ({ ...prev, ...updates }));\n  }, []);\n\n  // Generate video for a question\n  const generateQuestionVideo = useCallback(async (question: InterviewQuestion): Promise<string | null> => {\n    if (!didService || !didConfig) {\n      console.warn('D-ID service not configured, skipping video generation');\n      return null;\n    }\n\n    try {\n      updateState({ isGeneratingVideo: true, error: null });\n      \n      const videoUrl = await didService.generateQuestionVideo(question.text, {\n        sourceUrl: didConfig.sourceUrl,\n        voiceId: didConfig.voiceId,\n        provider: didConfig.provider,\n      });\n\n      return videoUrl;\n    } catch (error) {\n      console.error('Error generating question video:', error);\n      const errorMessage = error instanceof Error ? error.message : 'Failed to generate video';\n      updateState({ error: `Video generation failed: ${errorMessage}` });\n      return null;\n    } finally {\n      updateState({ isGeneratingVideo: false });\n    }\n  }, [didService, didConfig, updateState]);\n\n  // Start the interview\n  const startInterview = useCallback(async (customQuestions?: InterviewQuestion[]) => {\n    try {\n      const interviewQuestions = customQuestions || questions;\n      const session: InterviewSession = {\n        id: `interview_${Date.now()}`,\n        startTime: new Date(),\n        currentQuestionIndex: 0,\n        questions: interviewQuestions,\n        responses: [],\n        status: 'in_progress',\n      };\n\n      const firstQuestion = interviewQuestions[0];\n      let videoUrl: string | null = null;\n\n      // Generate video for first question if auto-generation is enabled\n      if (autoGenerateVideos && firstQuestion) {\n        videoUrl = await generateQuestionVideo(firstQuestion);\n      }\n\n      updateState({\n        session,\n        currentQuestion: firstQuestion,\n        currentVideoUrl: videoUrl,\n        userAnswer: '',\n        error: null,\n      });\n    } catch (error) {\n      const errorMessage = error instanceof Error ? error.message : 'Failed to start interview';\n      updateState({ error: `Failed to start interview: ${errorMessage}` });\n    }\n  }, [questions, autoGenerateVideos, generateQuestionVideo, updateState]);\n\n  // Submit answer and move to next question\n  const submitAnswer = useCallback(async (answer: string) => {\n    if (!state.session || !state.currentQuestion) {\n      throw new InterviewError('No active interview session');\n    }\n\n    if (!answer.trim()) {\n      updateState({ error: 'Please provide an answer before submitting' });\n      return;\n    }\n\n    try {\n      updateState({ isSubmitting: true, error: null });\n\n      // Create response record\n      const response: QuestionResponse = {\n        questionId: state.currentQuestion.id,\n        userAnswer: answer.trim(),\n        timestamp: new Date(),\n        videoUrl: state.currentVideoUrl || undefined,\n      };\n\n      // Update session with the response\n      const updatedSession: InterviewSession = {\n        ...state.session,\n        responses: [...state.session.responses, response],\n      };\n\n      updateState({\n        session: updatedSession,\n        userAnswer: '',\n      });\n\n      // Move to next question\n      await nextQuestion();\n    } catch (error) {\n      const errorMessage = error instanceof Error ? error.message : 'Failed to submit answer';\n      updateState({ error: `Failed to submit answer: ${errorMessage}` });\n    } finally {\n      updateState({ isSubmitting: false });\n    }\n  }, [state.session, state.currentQuestion, state.currentVideoUrl, updateState]);\n\n  // Move to next question\n  const nextQuestion = useCallback(async () => {\n    if (!state.session) {\n      throw new InterviewError('No active interview session');\n    }\n\n    const nextIndex = state.session.currentQuestionIndex + 1;\n    \n    if (nextIndex >= state.session.questions.length) {\n      // Interview completed\n      const completedSession: InterviewSession = {\n        ...state.session,\n        currentQuestionIndex: nextIndex,\n        status: 'completed',\n        endTime: new Date(),\n      };\n\n      updateState({\n        session: completedSession,\n        currentQuestion: null,\n        currentVideoUrl: null,\n      });\n      return;\n    }\n\n    // Move to next question\n    const nextQuestionObj = state.session.questions[nextIndex];\n    let videoUrl: string | null = null;\n\n    // Generate video for next question if auto-generation is enabled\n    if (autoGenerateVideos && nextQuestionObj) {\n      videoUrl = await generateQuestionVideo(nextQuestionObj);\n    }\n\n    const updatedSession: InterviewSession = {\n      ...state.session,\n      currentQuestionIndex: nextIndex,\n    };\n\n    updateState({\n      session: updatedSession,\n      currentQuestion: nextQuestionObj,\n      currentVideoUrl: videoUrl,\n      userAnswer: '',\n    });\n  }, [state.session, autoGenerateVideos, generateQuestionVideo, updateState]);\n\n  // Other actions\n  const pauseInterview = useCallback(() => {\n    if (state.session) {\n      updateState({\n        session: { ...state.session, status: 'paused' }\n      });\n    }\n  }, [state.session, updateState]);\n\n  const resumeInterview = useCallback(() => {\n    if (state.session) {\n      updateState({\n        session: { ...state.session, status: 'in_progress' }\n      });\n    }\n  }, [state.session, updateState]);\n\n  const completeInterview = useCallback(() => {\n    if (state.session) {\n      updateState({\n        session: { \n          ...state.session, \n          status: 'completed',\n          endTime: new Date()\n        }\n      });\n    }\n  }, [state.session, updateState]);\n\n  const resetInterview = useCallback(() => {\n    updateState({\n      session: null,\n      currentQuestion: null,\n      currentVideoUrl: null,\n      isGeneratingVideo: false,\n      isSubmitting: false,\n      userAnswer: '',\n      error: null,\n    });\n  }, [updateState]);\n\n  const setUserAnswer = useCallback((answer: string) => {\n    updateState({ userAnswer: answer });\n  }, [updateState]);\n\n  const clearError = useCallback(() => {\n    updateState({ error: null });\n  }, [updateState]);\n\n  // Actions object\n  const actions: SequentialInterviewActions = {\n    startInterview,\n    submitAnswer,\n    nextQuestion,\n    pauseInterview,\n    resumeInterview,\n    completeInterview,\n    resetInterview,\n    setUserAnswer,\n    clearError,\n  };\n\n  // Computed values\n  const isInterviewActive = state.session?.status === 'in_progress';\n  const isInterviewCompleted = state.session?.status === 'completed';\n  const currentQuestionNumber = state.session ? state.session.currentQuestionIndex + 1 : 0;\n  const totalQuestions = state.session?.questions.length || 0;\n  const progress = totalQuestions > 0 ? (currentQuestionNumber / totalQuestions) * 100 : 0;\n\n  return {\n    // State\n    ...state,\n    \n    // Actions\n    ...actions,\n    \n    // Computed values\n    isInterviewActive,\n    isInterviewCompleted,\n    currentQuestionNumber,\n    totalQuestions,\n    progress,\n    \n    // Utilities\n    canSubmit: !state.isSubmitting && !state.isGeneratingVideo && state.userAnswer.trim().length > 0,\n    hasError: !!state.error,\n  };\n}\n"], "names": [], "mappings": ";;;AAEA;AACA;AAWA;;AAdA;;;;AAsBO,SAAS,uBAAuB,UAAyC,CAAC,CAAC;;IAChF,MAAM,EACJ,SAAS,EACT,YAAY,qHAAA,CAAA,8BAA2B,EACvC,qBAAqB,IAAI,EAC1B,GAAG;IAEJ,QAAQ;IACR,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAA4B;QAC3D,SAAS;QACT,iBAAiB;QACjB,iBAAiB;QACjB,mBAAmB;QACnB,cAAc;QACd,YAAY;QACZ,OAAO;IACT;IAEA,wBAAwB;IACxB,MAAM,aAAa,YAAY,CAAA,GAAA,oHAAA,CAAA,sBAAmB,AAAD,EAAE,UAAU,MAAM,IAAI;IAEvE,kCAAkC;IAClC,MAAM,cAAc,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;2DAAE,CAAC;YAC/B;mEAAS,CAAA,OAAQ,CAAC;wBAAE,GAAG,IAAI;wBAAE,GAAG,OAAO;oBAAC,CAAC;;QAC3C;0DAAG,EAAE;IAEL,gCAAgC;IAChC,MAAM,wBAAwB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;qEAAE,OAAO;YAC/C,IAAI,CAAC,cAAc,CAAC,WAAW;gBAC7B,QAAQ,IAAI,CAAC;gBACb,OAAO;YACT;YAEA,IAAI;gBACF,YAAY;oBAAE,mBAAmB;oBAAM,OAAO;gBAAK;gBAEnD,MAAM,WAAW,MAAM,WAAW,qBAAqB,CAAC,SAAS,IAAI,EAAE;oBACrE,WAAW,UAAU,SAAS;oBAC9B,SAAS,UAAU,OAAO;oBAC1B,UAAU,UAAU,QAAQ;gBAC9B;gBAEA,OAAO;YACT,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,oCAAoC;gBAClD,MAAM,eAAe,iBAAiB,QAAQ,MAAM,OAAO,GAAG;gBAC9D,YAAY;oBAAE,OAAO,CAAC,yBAAyB,EAAE,cAAc;gBAAC;gBAChE,OAAO;YACT,SAAU;gBACR,YAAY;oBAAE,mBAAmB;gBAAM;YACzC;QACF;oEAAG;QAAC;QAAY;QAAW;KAAY;IAEvC,sBAAsB;IACtB,MAAM,iBAAiB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;8DAAE,OAAO;YACxC,IAAI;gBACF,MAAM,qBAAqB,mBAAmB;gBAC9C,MAAM,UAA4B;oBAChC,IAAI,CAAC,UAAU,EAAE,KAAK,GAAG,IAAI;oBAC7B,WAAW,IAAI;oBACf,sBAAsB;oBACtB,WAAW;oBACX,WAAW,EAAE;oBACb,QAAQ;gBACV;gBAEA,MAAM,gBAAgB,kBAAkB,CAAC,EAAE;gBAC3C,IAAI,WAA0B;gBAE9B,kEAAkE;gBAClE,IAAI,sBAAsB,eAAe;oBACvC,WAAW,MAAM,sBAAsB;gBACzC;gBAEA,YAAY;oBACV;oBACA,iBAAiB;oBACjB,iBAAiB;oBACjB,YAAY;oBACZ,OAAO;gBACT;YACF,EAAE,OAAO,OAAO;gBACd,MAAM,eAAe,iBAAiB,QAAQ,MAAM,OAAO,GAAG;gBAC9D,YAAY;oBAAE,OAAO,CAAC,2BAA2B,EAAE,cAAc;gBAAC;YACpE;QACF;6DAAG;QAAC;QAAW;QAAoB;QAAuB;KAAY;IAEtE,0CAA0C;IAC1C,MAAM,eAAe,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;4DAAE,OAAO;YACtC,IAAI,CAAC,MAAM,OAAO,IAAI,CAAC,MAAM,eAAe,EAAE;gBAC5C,MAAM,IAAI,qHAAA,CAAA,iBAAc,CAAC;YAC3B;YAEA,IAAI,CAAC,OAAO,IAAI,IAAI;gBAClB,YAAY;oBAAE,OAAO;gBAA6C;gBAClE;YACF;YAEA,IAAI;gBACF,YAAY;oBAAE,cAAc;oBAAM,OAAO;gBAAK;gBAE9C,yBAAyB;gBACzB,MAAM,WAA6B;oBACjC,YAAY,MAAM,eAAe,CAAC,EAAE;oBACpC,YAAY,OAAO,IAAI;oBACvB,WAAW,IAAI;oBACf,UAAU,MAAM,eAAe,IAAI;gBACrC;gBAEA,mCAAmC;gBACnC,MAAM,iBAAmC;oBACvC,GAAG,MAAM,OAAO;oBAChB,WAAW;2BAAI,MAAM,OAAO,CAAC,SAAS;wBAAE;qBAAS;gBACnD;gBAEA,YAAY;oBACV,SAAS;oBACT,YAAY;gBACd;gBAEA,wBAAwB;gBACxB,MAAM;YACR,EAAE,OAAO,OAAO;gBACd,MAAM,eAAe,iBAAiB,QAAQ,MAAM,OAAO,GAAG;gBAC9D,YAAY;oBAAE,OAAO,CAAC,yBAAyB,EAAE,cAAc;gBAAC;YAClE,SAAU;gBACR,YAAY;oBAAE,cAAc;gBAAM;YACpC;QACF;2DAAG;QAAC,MAAM,OAAO;QAAE,MAAM,eAAe;QAAE,MAAM,eAAe;QAAE;KAAY;IAE7E,wBAAwB;IACxB,MAAM,eAAe,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;4DAAE;YAC/B,IAAI,CAAC,MAAM,OAAO,EAAE;gBAClB,MAAM,IAAI,qHAAA,CAAA,iBAAc,CAAC;YAC3B;YAEA,MAAM,YAAY,MAAM,OAAO,CAAC,oBAAoB,GAAG;YAEvD,IAAI,aAAa,MAAM,OAAO,CAAC,SAAS,CAAC,MAAM,EAAE;gBAC/C,sBAAsB;gBACtB,MAAM,mBAAqC;oBACzC,GAAG,MAAM,OAAO;oBAChB,sBAAsB;oBACtB,QAAQ;oBACR,SAAS,IAAI;gBACf;gBAEA,YAAY;oBACV,SAAS;oBACT,iBAAiB;oBACjB,iBAAiB;gBACnB;gBACA;YACF;YAEA,wBAAwB;YACxB,MAAM,kBAAkB,MAAM,OAAO,CAAC,SAAS,CAAC,UAAU;YAC1D,IAAI,WAA0B;YAE9B,iEAAiE;YACjE,IAAI,sBAAsB,iBAAiB;gBACzC,WAAW,MAAM,sBAAsB;YACzC;YAEA,MAAM,iBAAmC;gBACvC,GAAG,MAAM,OAAO;gBAChB,sBAAsB;YACxB;YAEA,YAAY;gBACV,SAAS;gBACT,iBAAiB;gBACjB,iBAAiB;gBACjB,YAAY;YACd;QACF;2DAAG;QAAC,MAAM,OAAO;QAAE;QAAoB;QAAuB;KAAY;IAE1E,gBAAgB;IAChB,MAAM,iBAAiB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;8DAAE;YACjC,IAAI,MAAM,OAAO,EAAE;gBACjB,YAAY;oBACV,SAAS;wBAAE,GAAG,MAAM,OAAO;wBAAE,QAAQ;oBAAS;gBAChD;YACF;QACF;6DAAG;QAAC,MAAM,OAAO;QAAE;KAAY;IAE/B,MAAM,kBAAkB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;+DAAE;YAClC,IAAI,MAAM,OAAO,EAAE;gBACjB,YAAY;oBACV,SAAS;wBAAE,GAAG,MAAM,OAAO;wBAAE,QAAQ;oBAAc;gBACrD;YACF;QACF;8DAAG;QAAC,MAAM,OAAO;QAAE;KAAY;IAE/B,MAAM,oBAAoB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;iEAAE;YACpC,IAAI,MAAM,OAAO,EAAE;gBACjB,YAAY;oBACV,SAAS;wBACP,GAAG,MAAM,OAAO;wBAChB,QAAQ;wBACR,SAAS,IAAI;oBACf;gBACF;YACF;QACF;gEAAG;QAAC,MAAM,OAAO;QAAE;KAAY;IAE/B,MAAM,iBAAiB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;8DAAE;YACjC,YAAY;gBACV,SAAS;gBACT,iBAAiB;gBACjB,iBAAiB;gBACjB,mBAAmB;gBACnB,cAAc;gBACd,YAAY;gBACZ,OAAO;YACT;QACF;6DAAG;QAAC;KAAY;IAEhB,MAAM,gBAAgB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;6DAAE,CAAC;YACjC,YAAY;gBAAE,YAAY;YAAO;QACnC;4DAAG;QAAC;KAAY;IAEhB,MAAM,aAAa,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;0DAAE;YAC7B,YAAY;gBAAE,OAAO;YAAK;QAC5B;yDAAG;QAAC;KAAY;IAEhB,iBAAiB;IACjB,MAAM,UAAsC;QAC1C;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;IACF;IAEA,kBAAkB;IAClB,MAAM,oBAAoB,MAAM,OAAO,EAAE,WAAW;IACpD,MAAM,uBAAuB,MAAM,OAAO,EAAE,WAAW;IACvD,MAAM,wBAAwB,MAAM,OAAO,GAAG,MAAM,OAAO,CAAC,oBAAoB,GAAG,IAAI;IACvF,MAAM,iBAAiB,MAAM,OAAO,EAAE,UAAU,UAAU;IAC1D,MAAM,WAAW,iBAAiB,IAAI,AAAC,wBAAwB,iBAAkB,MAAM;IAEvF,OAAO;QACL,QAAQ;QACR,GAAG,KAAK;QAER,UAAU;QACV,GAAG,OAAO;QAEV,kBAAkB;QAClB;QACA;QACA;QACA;QACA;QAEA,YAAY;QACZ,WAAW,CAAC,MAAM,YAAY,IAAI,CAAC,MAAM,iBAAiB,IAAI,MAAM,UAAU,CAAC,IAAI,GAAG,MAAM,GAAG;QAC/F,UAAU,CAAC,CAAC,MAAM,KAAK;IACzB;AACF;GAxQgB", "debugId": null}}, {"offset": {"line": 1374, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Softwares/Ai%20bot/intview-ai/components/interview/SequentialInterview.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useEffect, useState } from 'react';\nimport { motion, AnimatePresence } from 'framer-motion';\nimport { ArrowR<PERSON>, Loader2, AlertCircle, Play, Pause } from 'lucide-react';\nimport { Button } from '@/components/ui/button';\nimport { useSequentialInterview } from '@/hooks/useSequentialInterview';\nimport { DIDConfig } from '@/types/interview';\nimport JobInfoCard from '@/components/JobInfoCard';\nimport InterviewLayout from '@/components/InterviewLayout';\n\ninterface SequentialInterviewProps {\n  onComplete?: () => void;\n  didConfig?: DIDConfig;\n  className?: string;\n}\n\nconst SequentialInterview: React.FC<SequentialInterviewProps> = ({\n  onComplete,\n  didConfig,\n  className = '',\n}) => {\n  const [textareaValue, setTextareaValue] = useState('');\n  \n  const {\n    session,\n    currentQuestion,\n    currentVideoUrl,\n    isGeneratingVideo,\n    isSubmitting,\n    userAnswer,\n    error,\n    isInterviewActive,\n    isInterviewCompleted,\n    currentQuestionNumber,\n    totalQuestions,\n    progress,\n    canSubmit,\n    hasError,\n    startInterview,\n    submitAnswer,\n    setUserAnswer,\n    clearError,\n  } = useSequentialInterview({\n    didConfig,\n    autoGenerateVideos: !!didConfig,\n  });\n\n  // Start interview on component mount\n  useEffect(() => {\n    if (!session) {\n      startInterview();\n    }\n  }, [session, startInterview]);\n\n  // Handle interview completion\n  useEffect(() => {\n    if (isInterviewCompleted && onComplete) {\n      onComplete();\n    }\n  }, [isInterviewCompleted, onComplete]);\n\n  // Handle form submission\n  const handleSubmit = async (e: React.FormEvent) => {\n    e.preventDefault();\n    if (canSubmit && textareaValue.trim()) {\n      await submitAnswer(textareaValue.trim());\n      setTextareaValue('');\n    }\n  };\n\n  // Handle textarea change\n  const handleTextareaChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {\n    const value = e.target.value;\n    setTextareaValue(value);\n    setUserAnswer(value);\n  };\n\n  // Handle key down for textarea\n  const handleKeyDown = (e: React.KeyboardEvent<HTMLTextAreaElement>) => {\n    if (e.key === 'Enter' && !e.shiftKey && canSubmit) {\n      e.preventDefault();\n      handleSubmit(e as any);\n    }\n  };\n\n  if (!session) {\n    return (\n      <div className=\"h-screen flex items-center justify-center\">\n        <div className=\"flex items-center space-x-2\">\n          <Loader2 className=\"w-6 h-6 animate-spin\" />\n          <span>Initializing interview...</span>\n        </div>\n      </div>\n    );\n  }\n\n  if (isInterviewCompleted) {\n    return (\n      <div className=\"h-screen\">\n        <JobInfoCard />\n        <InterviewLayout>\n          <div className=\"flex flex-col items-center justify-center space-y-6\">\n            <motion.div\n              initial={{ scale: 0.8, opacity: 0 }}\n              animate={{ scale: 1, opacity: 1 }}\n              className=\"text-center\"\n            >\n              <h2 className=\"text-3xl font-bold text-green-600 mb-4\">\n                Interview Completed!\n              </h2>\n              <p className=\"text-lg text-gray-600 mb-6\">\n                Thank you for completing the interview. Your responses have been recorded.\n              </p>\n              <div className=\"bg-green-50 border border-green-200 rounded-lg p-4\">\n                <p className=\"text-green-800\">\n                  You answered {session.responses.length} out of {totalQuestions} questions.\n                </p>\n              </div>\n            </motion.div>\n          </div>\n        </InterviewLayout>\n      </div>\n    );\n  }\n\n  return (\n    <div className={`h-screen ${className}`}>\n      <JobInfoCard />\n      \n      <InterviewLayout>\n        {/* Progress Bar */}\n        <div className=\"mb-6\">\n          <div className=\"flex justify-between items-center mb-2\">\n            <span className=\"text-sm font-medium text-gray-600\">\n              Question {currentQuestionNumber} of {totalQuestions}\n            </span>\n            <span className=\"text-sm font-medium text-gray-600\">\n              {Math.round(progress)}% Complete\n            </span>\n          </div>\n          <div className=\"w-full bg-gray-200 rounded-full h-2\">\n            <motion.div\n              className=\"bg-[#6938EF] h-2 rounded-full\"\n              initial={{ width: 0 }}\n              animate={{ width: `${progress}%` }}\n              transition={{ duration: 0.5 }}\n            />\n          </div>\n        </div>\n\n        <div className=\"flex flex-col lg:flex-row gap-6 lg:gap-10 justify-center items-center lg:items-start\">\n          {/* Left Side - Question and Input */}\n          <div className=\"w-full lg:w-1/2 max-w-md space-y-6\">\n            {/* Current Question */}\n            <AnimatePresence mode=\"wait\">\n              {currentQuestion && (\n                <motion.div\n                  key={currentQuestion.id}\n                  initial={{ opacity: 0, y: 20 }}\n                  animate={{ opacity: 1, y: 0 }}\n                  exit={{ opacity: 0, y: -20 }}\n                  transition={{ duration: 0.5 }}\n                  className=\"bg-white rounded-2xl p-6 shadow-sm border\"\n                >\n                  <h3 className=\"text-lg font-semibold text-[#6938EF] mb-4\">\n                    Question {currentQuestionNumber}\n                  </h3>\n                  <p className=\"text-gray-800 text-lg leading-relaxed\">\n                    {currentQuestion.text}\n                  </p>\n                </motion.div>\n              )}\n            </AnimatePresence>\n\n            {/* Answer Input */}\n            <motion.div\n              initial={{ opacity: 0, y: 20 }}\n              animate={{ opacity: 1, y: 0 }}\n              transition={{ delay: 0.2 }}\n              className=\"bg-white rounded-2xl p-6 shadow-sm border\"\n            >\n              <form onSubmit={handleSubmit} className=\"space-y-4\">\n                <div>\n                  <label htmlFor=\"answer\" className=\"block text-sm font-medium text-gray-700 mb-2\">\n                    Your Answer\n                  </label>\n                  <textarea\n                    id=\"answer\"\n                    value={textareaValue}\n                    onChange={handleTextareaChange}\n                    onKeyDown={handleKeyDown}\n                    placeholder=\"Type your answer here...\"\n                    className=\"w-full border-2 border-gray-200 rounded-xl px-4 py-3 resize-none focus:border-[#6938EF] focus:ring-2 focus:ring-[#6938EF]/20 transition-all duration-200 placeholder-gray-400\"\n                    rows={4}\n                    disabled={isSubmitting || isGeneratingVideo}\n                  />\n                </div>\n\n                {/* Error Display */}\n                <AnimatePresence>\n                  {hasError && (\n                    <motion.div\n                      initial={{ opacity: 0, height: 0 }}\n                      animate={{ opacity: 1, height: 'auto' }}\n                      exit={{ opacity: 0, height: 0 }}\n                      className=\"flex items-center space-x-2 text-red-600 bg-red-50 border border-red-200 rounded-lg p-3\"\n                    >\n                      <AlertCircle className=\"w-4 h-4 flex-shrink-0\" />\n                      <span className=\"text-sm\">{error}</span>\n                      <button\n                        type=\"button\"\n                        onClick={clearError}\n                        className=\"ml-auto text-red-400 hover:text-red-600\"\n                      >\n                        ×\n                      </button>\n                    </motion.div>\n                  )}\n                </AnimatePresence>\n\n                {/* Submit Button */}\n                <Button\n                  type=\"submit\"\n                  disabled={!canSubmit}\n                  className=\"w-full py-3 text-lg rounded-xl bg-[#6938EF] hover:bg-[#5a2fd8] disabled:bg-gray-300 disabled:cursor-not-allowed transition-all duration-200\"\n                >\n                  {isSubmitting ? (\n                    <div className=\"flex items-center space-x-2\">\n                      <Loader2 className=\"w-5 h-5 animate-spin\" />\n                      <span>Submitting...</span>\n                    </div>\n                  ) : (\n                    <div className=\"flex items-center space-x-2\">\n                      <span>Submit Answer</span>\n                      <ArrowRight className=\"w-5 h-5\" />\n                    </div>\n                  )}\n                </Button>\n              </form>\n            </motion.div>\n          </div>\n\n          {/* Right Side - Avatar Video */}\n          <div className=\"w-full lg:w-1/2 max-w-md\">\n            <motion.div\n              initial={{ opacity: 0, scale: 0.95 }}\n              animate={{ opacity: 1, scale: 1 }}\n              transition={{ delay: 0.3 }}\n              className=\"bg-gradient-to-br from-indigo-50 to-purple-50 rounded-2xl p-6 shadow-sm border aspect-video relative overflow-hidden\"\n            >\n              {isGeneratingVideo ? (\n                <div className=\"flex flex-col items-center justify-center h-full space-y-4\">\n                  <Loader2 className=\"w-12 h-12 animate-spin text-[#6938EF]\" />\n                  <p className=\"text-gray-600 text-center\">\n                    Generating AI avatar video...\n                  </p>\n                </div>\n              ) : currentVideoUrl ? (\n                <video\n                  src={currentVideoUrl}\n                  controls\n                  autoPlay\n                  className=\"w-full h-full object-cover rounded-lg\"\n                  onError={() => console.error('Error loading video')}\n                />\n              ) : (\n                <div className=\"flex flex-col items-center justify-center h-full space-y-4\">\n                  <div className=\"w-20 h-20 bg-gradient-to-br from-[#6938EF] to-[#8b5cf6] rounded-full flex items-center justify-center\">\n                    <Play className=\"w-8 h-8 text-white\" />\n                  </div>\n                  <div className=\"text-center\">\n                    <h3 className=\"text-lg font-semibold text-gray-800 mb-2\">\n                      AI Interviewer\n                    </h3>\n                    <p className=\"text-gray-600 text-sm\">\n                      {didConfig ? 'Video will appear here' : 'Text-based interview mode'}\n                    </p>\n                  </div>\n                </div>\n              )}\n            </motion.div>\n          </div>\n        </div>\n      </InterviewLayout>\n    </div>\n  );\n};\n\nexport default SequentialInterview;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AACA;AAAA;AAAA;AAAA;AACA;AACA;AAEA;AACA;;;AATA;;;;;;;;AAiBA,MAAM,sBAA0D,CAAC,EAC/D,UAAU,EACV,SAAS,EACT,YAAY,EAAE,EACf;;IACC,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEnD,MAAM,EACJ,OAAO,EACP,eAAe,EACf,eAAe,EACf,iBAAiB,EACjB,YAAY,EACZ,UAAU,EACV,KAAK,EACL,iBAAiB,EACjB,oBAAoB,EACpB,qBAAqB,EACrB,cAAc,EACd,QAAQ,EACR,SAAS,EACT,QAAQ,EACR,cAAc,EACd,YAAY,EACZ,aAAa,EACb,UAAU,EACX,GAAG,CAAA,GAAA,kIAAA,CAAA,yBAAsB,AAAD,EAAE;QACzB;QACA,oBAAoB,CAAC,CAAC;IACxB;IAEA,qCAAqC;IACrC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;yCAAE;YACR,IAAI,CAAC,SAAS;gBACZ;YACF;QACF;wCAAG;QAAC;QAAS;KAAe;IAE5B,8BAA8B;IAC9B,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;yCAAE;YACR,IAAI,wBAAwB,YAAY;gBACtC;YACF;QACF;wCAAG;QAAC;QAAsB;KAAW;IAErC,yBAAyB;IACzB,MAAM,eAAe,OAAO;QAC1B,EAAE,cAAc;QAChB,IAAI,aAAa,cAAc,IAAI,IAAI;YACrC,MAAM,aAAa,cAAc,IAAI;YACrC,iBAAiB;QACnB;IACF;IAEA,yBAAyB;IACzB,MAAM,uBAAuB,CAAC;QAC5B,MAAM,QAAQ,EAAE,MAAM,CAAC,KAAK;QAC5B,iBAAiB;QACjB,cAAc;IAChB;IAEA,+BAA+B;IAC/B,MAAM,gBAAgB,CAAC;QACrB,IAAI,EAAE,GAAG,KAAK,WAAW,CAAC,EAAE,QAAQ,IAAI,WAAW;YACjD,EAAE,cAAc;YAChB,aAAa;QACf;IACF;IAEA,IAAI,CAAC,SAAS;QACZ,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;kCACb,6LAAC,oNAAA,CAAA,UAAO;wBAAC,WAAU;;;;;;kCACnB,6LAAC;kCAAK;;;;;;;;;;;;;;;;;IAId;IAEA,IAAI,sBAAsB;QACxB,qBACE,6LAAC;YAAI,WAAU;;8BACb,6LAAC,6HAAA,CAAA,UAAW;;;;;8BACZ,6LAAC,iIAAA,CAAA,UAAe;8BACd,cAAA,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,SAAS;gCAAE,OAAO;gCAAK,SAAS;4BAAE;4BAClC,SAAS;gCAAE,OAAO;gCAAG,SAAS;4BAAE;4BAChC,WAAU;;8CAEV,6LAAC;oCAAG,WAAU;8CAAyC;;;;;;8CAGvD,6LAAC;oCAAE,WAAU;8CAA6B;;;;;;8CAG1C,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAE,WAAU;;4CAAiB;4CACd,QAAQ,SAAS,CAAC,MAAM;4CAAC;4CAAS;4CAAe;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAQ/E;IAEA,qBACE,6LAAC;QAAI,WAAW,CAAC,SAAS,EAAE,WAAW;;0BACrC,6LAAC,6HAAA,CAAA,UAAW;;;;;0BAEZ,6LAAC,iIAAA,CAAA,UAAe;;kCAEd,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAK,WAAU;;4CAAoC;4CACxC;4CAAsB;4CAAK;;;;;;;kDAEvC,6LAAC;wCAAK,WAAU;;4CACb,KAAK,KAAK,CAAC;4CAAU;;;;;;;;;;;;;0CAG1B,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oCACT,WAAU;oCACV,SAAS;wCAAE,OAAO;oCAAE;oCACpB,SAAS;wCAAE,OAAO,GAAG,SAAS,CAAC,CAAC;oCAAC;oCACjC,YAAY;wCAAE,UAAU;oCAAI;;;;;;;;;;;;;;;;;kCAKlC,6LAAC;wBAAI,WAAU;;0CAEb,6LAAC;gCAAI,WAAU;;kDAEb,6LAAC,4LAAA,CAAA,kBAAe;wCAAC,MAAK;kDACnB,iCACC,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4CAET,SAAS;gDAAE,SAAS;gDAAG,GAAG;4CAAG;4CAC7B,SAAS;gDAAE,SAAS;gDAAG,GAAG;4CAAE;4CAC5B,MAAM;gDAAE,SAAS;gDAAG,GAAG,CAAC;4CAAG;4CAC3B,YAAY;gDAAE,UAAU;4CAAI;4CAC5B,WAAU;;8DAEV,6LAAC;oDAAG,WAAU;;wDAA4C;wDAC9C;;;;;;;8DAEZ,6LAAC;oDAAE,WAAU;8DACV,gBAAgB,IAAI;;;;;;;2CAXlB,gBAAgB,EAAE;;;;;;;;;;kDAkB7B,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wCACT,SAAS;4CAAE,SAAS;4CAAG,GAAG;wCAAG;wCAC7B,SAAS;4CAAE,SAAS;4CAAG,GAAG;wCAAE;wCAC5B,YAAY;4CAAE,OAAO;wCAAI;wCACzB,WAAU;kDAEV,cAAA,6LAAC;4CAAK,UAAU;4CAAc,WAAU;;8DACtC,6LAAC;;sEACC,6LAAC;4DAAM,SAAQ;4DAAS,WAAU;sEAA+C;;;;;;sEAGjF,6LAAC;4DACC,IAAG;4DACH,OAAO;4DACP,UAAU;4DACV,WAAW;4DACX,aAAY;4DACZ,WAAU;4DACV,MAAM;4DACN,UAAU,gBAAgB;;;;;;;;;;;;8DAK9B,6LAAC,4LAAA,CAAA,kBAAe;8DACb,0BACC,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wDACT,SAAS;4DAAE,SAAS;4DAAG,QAAQ;wDAAE;wDACjC,SAAS;4DAAE,SAAS;4DAAG,QAAQ;wDAAO;wDACtC,MAAM;4DAAE,SAAS;4DAAG,QAAQ;wDAAE;wDAC9B,WAAU;;0EAEV,6LAAC,uNAAA,CAAA,cAAW;gEAAC,WAAU;;;;;;0EACvB,6LAAC;gEAAK,WAAU;0EAAW;;;;;;0EAC3B,6LAAC;gEACC,MAAK;gEACL,SAAS;gEACT,WAAU;0EACX;;;;;;;;;;;;;;;;;8DAQP,6LAAC,8HAAA,CAAA,SAAM;oDACL,MAAK;oDACL,UAAU,CAAC;oDACX,WAAU;8DAET,6BACC,6LAAC;wDAAI,WAAU;;0EACb,6LAAC,oNAAA,CAAA,UAAO;gEAAC,WAAU;;;;;;0EACnB,6LAAC;0EAAK;;;;;;;;;;;6EAGR,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;0EAAK;;;;;;0EACN,6LAAC,qNAAA,CAAA,aAAU;gEAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0CASlC,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oCACT,SAAS;wCAAE,SAAS;wCAAG,OAAO;oCAAK;oCACnC,SAAS;wCAAE,SAAS;wCAAG,OAAO;oCAAE;oCAChC,YAAY;wCAAE,OAAO;oCAAI;oCACzB,WAAU;8CAET,kCACC,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,oNAAA,CAAA,UAAO;gDAAC,WAAU;;;;;;0DACnB,6LAAC;gDAAE,WAAU;0DAA4B;;;;;;;;;;;+CAIzC,gCACF,6LAAC;wCACC,KAAK;wCACL,QAAQ;wCACR,QAAQ;wCACR,WAAU;wCACV,SAAS,IAAM,QAAQ,KAAK,CAAC;;;;;6DAG/B,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC,qMAAA,CAAA,OAAI;oDAAC,WAAU;;;;;;;;;;;0DAElB,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAG,WAAU;kEAA2C;;;;;;kEAGzD,6LAAC;wDAAE,WAAU;kEACV,YAAY,2BAA2B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAW9D;GA9QM;;QA0BA,kIAAA,CAAA,yBAAsB;;;KA1BtB;uCAgRS", "debugId": null}}, {"offset": {"line": 2009, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Softwares/Ai%20bot/intview-ai/components/interview/InterviewRecording.tsx"], "sourcesContent": ["import { <PERSON><PERSON><PERSON> } from \"lucide-react\";\nimport JobInfoCard from \"@/components/JobInfoCard\";\nimport QuestionsList from \"@/components/QuestionsList\";\nimport CandidateImage from \"@/components/CandidateImage\";\nimport InterviewLayout from \"@/components/InterviewLayout\";\nimport { But<PERSON> } from \"@/components/ui/button\";\n\ntype InterviewRecordingProps = {\n  onNext?: () => void;\n};\n\nconst InterviewRecording = ({ onNext }: InterviewRecordingProps) => {\n  return (\n    <div className=\"h-screen\">\n      <JobInfoCard />\n\n      <InterviewLayout>\n        <div className=\"flex flex-col md:flex-row gap-10 justify-center items-center md:items-start\">\n          <QuestionsList className=\"h-[550px]\" />\n          <CandidateImage />\n        </div>\n\n        <div className=\"flex justify-center mt-10 gap-4\">\n          <Button\n            // disabled\n            variant=\"default\"\n            className=\"py-2 sm:py-6 text-sm sm:text-md rounded-full w-full sm:w-[200px] lg:w-[330px] flex items-center gap-2 group cursor-pointer text-white\"\n            onClick={() => onNext && onNext()}\n          >\n            Start Interview\n            <ArrowRight className=\"w-6 h-6 duration-300 group-hover:translate-x-1\" />\n          </Button>\n        </div>\n        <div className=\"flex justify-center mt-5 text-2xl font-semibold text-primary\">\n          02:00\n        </div>\n      </InterviewLayout>\n    </div>\n  );\n};\n\nexport default InterviewRecording;\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AACA;AACA;AACA;;;;;;;;AAMA,MAAM,qBAAqB,CAAC,EAAE,MAAM,EAA2B;IAC7D,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC,6HAAA,CAAA,UAAW;;;;;0BAEZ,6LAAC,iIAAA,CAAA,UAAe;;kCACd,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,+HAAA,CAAA,UAAa;gCAAC,WAAU;;;;;;0CACzB,6LAAC,gIAAA,CAAA,UAAc;;;;;;;;;;;kCAGjB,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC,8HAAA,CAAA,SAAM;4BACL,WAAW;4BACX,SAAQ;4BACR,WAAU;4BACV,SAAS,IAAM,UAAU;;gCAC1B;8CAEC,6LAAC,qNAAA,CAAA,aAAU;oCAAC,WAAU;;;;;;;;;;;;;;;;;kCAG1B,6LAAC;wBAAI,WAAU;kCAA+D;;;;;;;;;;;;;;;;;;AAMtF;KA5BM;uCA8BS", "debugId": null}}, {"offset": {"line": 2119, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Softwares/Ai%20bot/intview-ai/components/VideoTranscript.tsx"], "sourcesContent": ["const VideoTranscript = () => {\r\n  return (\r\n    <div className=\"rounded-2xl bg-white p-4 w-full max-w-[300px] sm:w-[300px] shadow-sm h-[488px] overflow-y-auto scrollbar-hidden\">\r\n      <p className=\"text-lg font-semibold text-black mb-5\">Video Transcript</p>\r\n      <p>Tell us about yourselves?</p>\r\n      <p className=\"text-sm mt-4 leading-7 \">\r\n        Motivated and results-driven professional with a proven track record of\r\n        success in dynamic work environments. Known for strong problem-solving\r\n        skills, a collaborative mindset, and a dedication to continuous learning\r\n        and improvement. Brings a blend of technical expertise, strategic\r\n        thinking, and effective communication to contribute meaningfully to team\r\n        and organizational goals. Eager to take on new challenges and deliver\r\n        impactful outcomes in a fast-paced role.\r\n      </p>\r\n    </div>\r\n  );\r\n};\r\nexport default VideoTranscript;\r\n"], "names": [], "mappings": ";;;;;AAAA,MAAM,kBAAkB;IACtB,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAE,WAAU;0BAAwC;;;;;;0BACrD,6LAAC;0BAAE;;;;;;0BACH,6LAAC;gBAAE,WAAU;0BAA0B;;;;;;;;;;;;AAW7C;KAhBM;uCAiBS", "debugId": null}}, {"offset": {"line": 2171, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Softwares/Ai%20bot/intview-ai/components/interview/FinishInterview.tsx"], "sourcesContent": ["import { ArrowR<PERSON> } from \"lucide-react\";\nimport JobInfoCard from \"@/components/JobInfoCard\";\nimport QuestionsList from \"@/components/QuestionsList\";\nimport CandidateImage from \"@/components/CandidateImage\";\nimport InterviewLayout from \"@/components/InterviewLayout\";\nimport { But<PERSON> } from \"@/components/ui/button\";\nimport VideoTranscript from \"@/components/VideoTranscript\";\n\ntype FinishInterviewProps = {\n  onNext?: () => void;\n};\n\nconst FinishInterview = ({ onNext }: FinishInterviewProps) => {\n  return (\n    <div className=\"h-screen\">\n      <JobInfoCard />\n\n      <InterviewLayout>\n        <div className=\"flex flex-col lg:flex-row gap-6 lg:gap-10 justify-center items-center lg:items-start\">\n          <QuestionsList />\n          <CandidateImage className=\"w-[265px]\" />\n          <VideoTranscript />\n        </div>\n\n        <div className=\"flex justify-center mt-10 gap-4\">\n          <Button\n            variant=\"default\"\n            className=\"py-2 sm:py-6 text-sm sm:text-md rounded-full w-full sm:w-[200px] lg:w-[330px] flex items-center gap-2 group cursor-pointer text-white\"\n            onClick={() => onNext && onNext()}\n          >\n            Finish Interview\n            <ArrowRight className=\"w-6 h-6 duration-300 group-hover:translate-x-1\" />\n          </Button>\n        </div>\n      </InterviewLayout>\n    </div>\n  );\n};\n\nexport default FinishInterview;\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;AAMA,MAAM,kBAAkB,CAAC,EAAE,MAAM,EAAwB;IACvD,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC,6HAAA,CAAA,UAAW;;;;;0BAEZ,6LAAC,iIAAA,CAAA,UAAe;;kCACd,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,+HAAA,CAAA,UAAa;;;;;0CACd,6LAAC,gIAAA,CAAA,UAAc;gCAAC,WAAU;;;;;;0CAC1B,6LAAC,iIAAA,CAAA,UAAe;;;;;;;;;;;kCAGlB,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC,8HAAA,CAAA,SAAM;4BACL,SAAQ;4BACR,WAAU;4BACV,SAAS,IAAM,UAAU;;gCAC1B;8CAEC,6LAAC,qNAAA,CAAA,aAAU;oCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAMlC;KAzBM;uCA2BS", "debugId": null}}, {"offset": {"line": 2284, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Softwares/Ai%20bot/intview-ai/public/icons/trophy.png.mjs%20%28structured%20image%20object%29"], "sourcesContent": ["import src from \"IMAGE\";\nexport default { src, width: 28, height: 28, blurDataURL: \"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAAICAYAAADED76LAAABBUlEQVR42i2JMUvDQBiGv2qpl6slTbSRNLFeqqFncrVJsATUap266F/ooIOLSCY1o11KKDoodWjBRURE0MFdf4H/RLu5Su9KX3jg4X0AS7NoaTEjk2XJCFlufZvjcxefaKBrGXV/S6lusqwdUOwEVHICF9v82yjyBod7ar1Zy3o3UaH7dbvyLbg+K3R3mOQdNJU6BBQ5/UhO3nrk8e+h8S94534X5ROfN6hSTC/a8vHTlX7/8xKOfl/D0XNH75+380esgitAinMLrV3Fd8rI6pxocXKqxcxGVquh+MRAKqRSADV3fpWYSB/G5mB4aQ6Ee25uTbTJhJTNdOmjZ3wKLCNdmpnGMewcPLJUc9zPAAAAAElFTkSuQmCC\", blurWidth: 8, blurHeight: 8 }\n"], "names": [], "mappings": ";;;AAAA;;uCACe;IAAE,KAAA,kHAAA,CAAA,UAAG;IAAE,OAAO;IAAI,QAAQ;IAAI,aAAa;IAAkc,WAAW;IAAG,YAAY;AAAE", "debugId": null}}, {"offset": {"line": 2306, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Softwares/Ai%20bot/intview-ai/components/InterviewCard.tsx"], "sourcesContent": ["import React from \"react\";\r\nimport Image from \"next/image\";\r\nimport TROPHY from \"@/public/icons/trophy.png\";\r\nconst InterviewCard = () => {\r\n  return (\r\n    <div className=\"flex  justify-between bg-white rounded-2xl shadow-md p-4 w-full max-w-xl mb-5\">\r\n      {/* Left Box: Score Section */}\r\n      <div className=\"flex items-center space-x-4\">\r\n        <div className=\"bg-[#F4F1FE] rounded-xl px-4 py-4 text-center w-30\">\r\n          <div className=\"flex justify-center mb-2\">\r\n            <Image src={TROPHY} alt=\"Trophy\" />\r\n          </div>\r\n          <p className=\"text-xl font-bold text-[#1E1E1E]\">55%</p>\r\n          <p className=\"text-xs text-gray-600 mt-1\">Overall Score</p>\r\n        </div>\r\n\r\n        <div>\r\n          <h3 className=\"font-semibold text-sm sm:text-[6px] md:text-base lg:text-lg text-[#1E1E1E] mb-2\">\r\n            AI Interviewer\r\n          </h3>\r\n          <p className=\"text-sm text-gray-800 font-medium\">UI UX Designer</p>\r\n          <p className=\"text-sm text-gray-800 font-medium\">18th June, 2025</p>\r\n        </div>\r\n      </div>\r\n\r\n      <div className=\"top-0\">\r\n        <span className=\"bg-[#CCFFB1] text-[#1E1E1E] text-xs px-4 py-1 rounded-full\">\r\n          Evaluated\r\n        </span>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default InterviewCard;\r\n"], "names": [], "mappings": ";;;;AACA;AACA;;;;AACA,MAAM,gBAAgB;IACpB,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC,gIAAA,CAAA,UAAK;oCAAC,KAAK,qRAAA,CAAA,UAAM;oCAAE,KAAI;;;;;;;;;;;0CAE1B,6LAAC;gCAAE,WAAU;0CAAmC;;;;;;0CAChD,6LAAC;gCAAE,WAAU;0CAA6B;;;;;;;;;;;;kCAG5C,6LAAC;;0CACC,6LAAC;gCAAG,WAAU;0CAAkF;;;;;;0CAGhG,6LAAC;gCAAE,WAAU;0CAAoC;;;;;;0CACjD,6LAAC;gCAAE,WAAU;0CAAoC;;;;;;;;;;;;;;;;;;0BAIrD,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAK,WAAU;8BAA6D;;;;;;;;;;;;;;;;;AAMrF;KA7BM;uCA+BS", "debugId": null}}, {"offset": {"line": 2435, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Softwares/Ai%20bot/intview-ai/components/analysis/ScoreBar.jsx"], "sourcesContent": ["const ScoreBar = ({ label, value, color = \"bg-orange-500\" }) => {\r\n  return (\r\n    <div className=\"mb-2\">\r\n      <div className=\"flex justify-between text-sm mb-1\">\r\n        <span className=\"mb-1\">{label}</span>\r\n        <span>{value}/100</span>\r\n      </div>\r\n      <div className=\"w-full bg-gray-200 rounded-full h-2.5\">\r\n        <div\r\n          className={`h-2.5 rounded-full ${color}`}\r\n          style={{ width: `${value}%` }}\r\n        ></div>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default ScoreBar;\r\n"], "names": [], "mappings": ";;;;;AAAA,MAAM,WAAW,CAAC,EAAE,KAAK,EAAE,KAAK,EAAE,QAAQ,eAAe,EAAE;IACzD,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAK,WAAU;kCAAQ;;;;;;kCACxB,6LAAC;;4BAAM;4BAAM;;;;;;;;;;;;;0BAEf,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBACC,WAAW,CAAC,mBAAmB,EAAE,OAAO;oBACxC,OAAO;wBAAE,OAAO,GAAG,MAAM,CAAC,CAAC;oBAAC;;;;;;;;;;;;;;;;;AAKtC;KAfM;uCAiBS", "debugId": null}}, {"offset": {"line": 2508, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Softwares/Ai%20bot/intview-ai/components/analysis/CircularRating.jsx"], "sourcesContent": ["import { CircularProgressbar, buildStyles } from \"react-circular-progressbar\";\r\nimport \"react-circular-progressbar/dist/styles.css\";\r\n\r\nconst CircularRating = ({ label, percent, color, trailColor }) => {\r\n  return (\r\n    <div className=\"flex flex-col items-center space-y-1 mb-2\">\r\n      <p className=\"text-sm font-semibold mb-3\">{label}</p>\r\n      <div className=\"w-32 h-28\">\r\n        <CircularProgressbar\r\n          value={percent}\r\n          text={`${percent}%`}\r\n          strokeWidth={10}\r\n          styles={buildStyles({\r\n            textSize: \"12px\",\r\n            pathColor: color,\r\n            textColor: \"#5a5a5a\",\r\n            trailColor: trailColor,\r\n          })}\r\n        />\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default CircularRating;\r\n"], "names": [], "mappings": ";;;;AAAA;;;;AAGA,MAAM,iBAAiB,CAAC,EAAE,KAAK,EAAE,OAAO,EAAE,KAAK,EAAE,UAAU,EAAE;IAC3D,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAE,WAAU;0BAA8B;;;;;;0BAC3C,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC,2KAAA,CAAA,sBAAmB;oBAClB,OAAO;oBACP,MAAM,GAAG,QAAQ,CAAC,CAAC;oBACnB,aAAa;oBACb,QAAQ,CAAA,GAAA,2KAAA,CAAA,cAAW,AAAD,EAAE;wBAClB,UAAU;wBACV,WAAW;wBACX,WAAW;wBACX,YAAY;oBACd;;;;;;;;;;;;;;;;;AAKV;KAnBM;uCAqBS", "debugId": null}}, {"offset": {"line": 2570, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Softwares/Ai%20bot/intview-ai/components/analysis/ScoreCard.jsx"], "sourcesContent": ["import ScoreBar from \"./ScoreBar\";\r\nimport CircularRating from \"./CircularRating\";\r\n\r\nconst ScoreCard = () => {\r\n  return (\r\n    <div className=\"grid grid-cols-1 sm:grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 border p-6 rounded-xl w-full max-w-6xl mx-auto\">\r\n      {/* Resume Score */}\r\n      <div className=\"bg-white rounded-lg p-4 shadow-sm\">\r\n        <div className=\"flex justify-between font-semibold mb-4\">\r\n          <span>Resume Score</span>\r\n          <span>65%</span>\r\n        </div>\r\n        <div className=\"flex flex-col gap-4\">\r\n          <ScoreBar label=\"Company Fit\" value={66} />\r\n          <ScoreBar\r\n            label=\"Relevant Experience\"\r\n            value={66}\r\n            color=\"bg-purple-600\"\r\n          />\r\n          <ScoreBar label=\"Job Knowledge\" value={66} />\r\n          <ScoreBar label=\"Education\" value={66} />\r\n          <ScoreBar label=\"Hard Skills\" value={66} />\r\n        </div>\r\n\r\n        <div className=\"mt-4 font-medium flex justify-between bg-gray-100 text-sm text-center border rounded-xl p-8\">\r\n          Over All Score &nbsp; <span className=\"text-black\">66/100</span>\r\n        </div>\r\n      </div>\r\n\r\n      {/* Video Score */}\r\n      <div className=\"bg-white rounded-lg p-4 shadow-sm\">\r\n        <div className=\"font-semibold mb-4\">Video Score</div>\r\n        <div className=\"flex flex-col gap-4\">\r\n          <ScoreBar label=\"Professionalism\" value={64} />\r\n          <ScoreBar label=\"Energy Level\" value={56} color=\"bg-purple-600\" />\r\n          <ScoreBar label=\"Communication\" value={58} />\r\n          <ScoreBar label=\"Sociability\" value={70} />\r\n        </div>\r\n      </div>\r\n\r\n      {/* AI Ratings */}\r\n      <div className=\"bg-white rounded-lg p-4 flex flex-col space-y-2   gap-5 shadow-sm\">\r\n        <p className=\"font-semibold\">AI Rating</p>\r\n        <CircularRating\r\n          label=\"AI Resume Rating\"\r\n          percent={75}\r\n          color=\"#A855F7\"\r\n          trailColor=\"#EAE2FF\"\r\n        />\r\n        <CircularRating\r\n          label=\"AI Video Rating\"\r\n          percent={75}\r\n          color=\"#FF5B00\"\r\n          trailColor=\"#FFEAE1\"\r\n        />\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default ScoreCard;\r\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;;AAEA,MAAM,YAAY;IAChB,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;0CAAK;;;;;;0CACN,6LAAC;0CAAK;;;;;;;;;;;;kCAER,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,sIAAA,CAAA,UAAQ;gCAAC,OAAM;gCAAc,OAAO;;;;;;0CACrC,6LAAC,sIAAA,CAAA,UAAQ;gCACP,OAAM;gCACN,OAAO;gCACP,OAAM;;;;;;0CAER,6LAAC,sIAAA,CAAA,UAAQ;gCAAC,OAAM;gCAAgB,OAAO;;;;;;0CACvC,6LAAC,sIAAA,CAAA,UAAQ;gCAAC,OAAM;gCAAY,OAAO;;;;;;0CACnC,6LAAC,sIAAA,CAAA,UAAQ;gCAAC,OAAM;gCAAc,OAAO;;;;;;;;;;;;kCAGvC,6LAAC;wBAAI,WAAU;;4BAA8F;0CACrF,6LAAC;gCAAK,WAAU;0CAAa;;;;;;;;;;;;;;;;;;0BAKvD,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;kCAAqB;;;;;;kCACpC,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,sIAAA,CAAA,UAAQ;gCAAC,OAAM;gCAAkB,OAAO;;;;;;0CACzC,6LAAC,sIAAA,CAAA,UAAQ;gCAAC,OAAM;gCAAe,OAAO;gCAAI,OAAM;;;;;;0CAChD,6LAAC,sIAAA,CAAA,UAAQ;gCAAC,OAAM;gCAAgB,OAAO;;;;;;0CACvC,6LAAC,sIAAA,CAAA,UAAQ;gCAAC,OAAM;gCAAc,OAAO;;;;;;;;;;;;;;;;;;0BAKzC,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAE,WAAU;kCAAgB;;;;;;kCAC7B,6LAAC,4IAAA,CAAA,UAAc;wBACb,OAAM;wBACN,SAAS;wBACT,OAAM;wBACN,YAAW;;;;;;kCAEb,6LAAC,4IAAA,CAAA,UAAc;wBACb,OAAM;wBACN,SAAS;wBACT,OAAM;wBACN,YAAW;;;;;;;;;;;;;;;;;;AAKrB;KAvDM;uCAyDS", "debugId": null}}, {"offset": {"line": 2799, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Softwares/Ai%20bot/intview-ai/components/interview/Analysis.tsx"], "sourcesContent": ["// import JobInfoCard from \"@/components/JobInfoCard\";\r\nimport QuestionsList from \"@/components/QuestionsList\";\r\nimport CandidateImage from \"@/components/CandidateImage\";\r\nimport InterviewLayout from \"@/components/InterviewLayout\";\r\nimport VideoTranscript from \"@/components/VideoTranscript\";\r\nimport InterviewCard from \"@/components/InterviewCard\";\r\nimport ScoreCard from \"../analysis/ScoreCard\";\r\n\r\nconst Analysis = () => {\r\n  return (\r\n    <div className=\"h-screen\">\r\n      <InterviewCard />\r\n      <InterviewLayout>\r\n        <div className=\"flex flex-col lg:flex-row gap-6 lg:gap-10 justify-center items-center lg:items-start\">\r\n          <QuestionsList />\r\n          <CandidateImage className=\"w-[265px]\" />\r\n          <VideoTranscript />\r\n        </div>\r\n      </InterviewLayout>\r\n      <ScoreCard />\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default Analysis;\r\n"], "names": [], "mappings": "AAAA,sDAAsD;;;;;AACtD;AACA;AACA;AACA;AACA;AACA;;;;;;;;AAEA,MAAM,WAAW;IACf,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC,+HAAA,CAAA,UAAa;;;;;0BACd,6LAAC,iIAAA,CAAA,UAAe;0BACd,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC,+HAAA,CAAA,UAAa;;;;;sCACd,6LAAC,gIAAA,CAAA,UAAc;4BAAC,WAAU;;;;;;sCAC1B,6LAAC,iIAAA,CAAA,UAAe;;;;;;;;;;;;;;;;0BAGpB,6LAAC,uIAAA,CAAA,UAAS;;;;;;;;;;;AAGhB;KAdM;uCAgBS", "debugId": null}}, {"offset": {"line": 2883, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Softwares/Ai%20bot/intview-ai/lib/config.ts"], "sourcesContent": ["// Configuration for the interview application\n\nexport interface AppConfig {\n  did: {\n    apiKey: string;\n    sourceUrl?: string;\n    voiceId?: string;\n    provider?: string;\n  };\n  interview: {\n    enableVideoGeneration: boolean;\n    maxQuestions: number;\n    defaultTimeout: number;\n  };\n}\n\n// Default configuration\nconst defaultConfig: AppConfig = {\n  did: {\n    apiKey: process.env.NEXT_PUBLIC_DID_API_KEY || '',\n    sourceUrl: process.env.NEXT_PUBLIC_DID_SOURCE_URL,\n    voiceId: process.env.NEXT_PUBLIC_DID_VOICE_ID,\n    provider: process.env.NEXT_PUBLIC_DID_PROVIDER || 'microsoft',\n  },\n  interview: {\n    enableVideoGeneration: process.env.NEXT_PUBLIC_ENABLE_VIDEO_GENERATION === 'true',\n    maxQuestions: parseInt(process.env.NEXT_PUBLIC_MAX_QUESTIONS || '10'),\n    defaultTimeout: parseInt(process.env.NEXT_PUBLIC_DEFAULT_TIMEOUT || '30000'),\n  },\n};\n\n// Get configuration with validation\nexport function getConfig(): AppConfig {\n  const config = { ...defaultConfig };\n  \n  // Validate required fields\n  if (config.interview.enableVideoGeneration && !config.did.apiKey) {\n    console.warn('D-ID API key not found. Video generation will be disabled.');\n    config.interview.enableVideoGeneration = false;\n  }\n  \n  return config;\n}\n\n// Get D-ID configuration\nexport function getDIDConfig() {\n  const config = getConfig();\n  \n  if (!config.interview.enableVideoGeneration || !config.did.apiKey) {\n    return null;\n  }\n  \n  return {\n    apiKey: config.did.apiKey,\n    sourceUrl: config.did.sourceUrl,\n    voiceId: config.did.voiceId,\n    provider: config.did.provider,\n  };\n}\n\n// Check if video generation is enabled\nexport function isVideoGenerationEnabled(): boolean {\n  const config = getConfig();\n  return config.interview.enableVideoGeneration && !!config.did.apiKey;\n}\n"], "names": [], "mappings": "AAAA,8CAA8C;;;;;;AAmBlC;AAHZ,wBAAwB;AACxB,MAAM,gBAA2B;IAC/B,KAAK;QACH,QAAQ,gKAAA,CAAA,UAAO,CAAC,GAAG,CAAC,uBAAuB,IAAI;QAC/C,WAAW,gKAAA,CAAA,UAAO,CAAC,GAAG,CAAC,0BAA0B;QACjD,SAAS,gKAAA,CAAA,UAAO,CAAC,GAAG,CAAC,wBAAwB;QAC7C,UAAU,gKAAA,CAAA,UAAO,CAAC,GAAG,CAAC,wBAAwB,IAAI;IACpD;IACA,WAAW;QACT,uBAAuB,gKAAA,CAAA,UAAO,CAAC,GAAG,CAAC,mCAAmC,KAAK;QAC3E,cAAc,SAAS,gKAAA,CAAA,UAAO,CAAC,GAAG,CAAC,yBAAyB,IAAI;QAChE,gBAAgB,SAAS,gKAAA,CAAA,UAAO,CAAC,GAAG,CAAC,2BAA2B,IAAI;IACtE;AACF;AAGO,SAAS;IACd,MAAM,SAAS;QAAE,GAAG,aAAa;IAAC;IAElC,2BAA2B;IAC3B,IAAI,OAAO,SAAS,CAAC,qBAAqB,IAAI,CAAC,OAAO,GAAG,CAAC,MAAM,EAAE;QAChE,QAAQ,IAAI,CAAC;QACb,OAAO,SAAS,CAAC,qBAAqB,GAAG;IAC3C;IAEA,OAAO;AACT;AAGO,SAAS;IACd,MAAM,SAAS;IAEf,IAAI,CAAC,OAAO,SAAS,CAAC,qBAAqB,IAAI,CAAC,OAAO,GAAG,CAAC,MAAM,EAAE;QACjE,OAAO;IACT;IAEA,OAAO;QACL,QAAQ,OAAO,GAAG,CAAC,MAAM;QACzB,WAAW,OAAO,GAAG,CAAC,SAAS;QAC/B,SAAS,OAAO,GAAG,CAAC,OAAO;QAC3B,UAAU,OAAO,GAAG,CAAC,QAAQ;IAC/B;AACF;AAGO,SAAS;IACd,MAAM,SAAS;IACf,OAAO,OAAO,SAAS,CAAC,qBAAqB,IAAI,CAAC,CAAC,OAAO,GAAG,CAAC,MAAM;AACtE", "debugId": null}}, {"offset": {"line": 2940, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Softwares/Ai%20bot/intview-ai/app/%28root%29/interview/page.tsx"], "sourcesContent": ["\"use client\";\r\nimport React, { useState } from \"react\";\r\nimport InterviewInstructions from \"@/components/interview/InterviewInstructions\";\r\nimport QuestionsPage from \"@/components/interview/QuestionsPage\";\r\nimport SequentialInterview from \"@/components/interview/SequentialInterview\";\r\nimport InterviewRecording from \"@/components/interview/InterviewRecording\";\r\nimport FinishInterview from \"@/components/interview/FinishInterview\";\r\nimport Analysis from \"@/components/interview/Analysis\";\r\nimport { getDIDConfig } from \"@/lib/config\";\r\n\r\ntype InterviewStep =\r\n  | \"instructions\"\r\n  | \"questions\"\r\n  | \"sequentialInterview\"\r\n  | \"recording\"\r\n  | \"finishInterview\"\r\n  | \"analysis\";\r\n\r\nconst Interview = () => {\r\n  const [currentStep, setCurrentStep] = useState<InterviewStep>(\"instructions\");\r\n  const didConfig = getDIDConfig();\r\n\r\n  const renderCurrentComponent = () => {\r\n    switch (currentStep) {\r\n      case \"instructions\":\r\n        return (\r\n          <InterviewInstructions onNext={() => setCurrentStep(\"questions\")} />\r\n        );\r\n      case \"questions\":\r\n        return <QuestionsPage onNext={() => setCurrentStep(\"sequentialInterview\")} />;\r\n      case \"sequentialInterview\":\r\n        return (\r\n          <SequentialInterview\r\n            onComplete={() => setCurrentStep(\"finishInterview\")}\r\n            didConfig={didConfig || undefined}\r\n          />\r\n        );\r\n      case \"recording\":\r\n        return (\r\n          <InterviewRecording\r\n            onNext={() => setCurrentStep(\"finishInterview\")}\r\n          />\r\n        );\r\n      case \"finishInterview\":\r\n        return <FinishInterview onNext={() => setCurrentStep(\"analysis\")} />;\r\n\r\n      case \"analysis\":\r\n        return <Analysis />;\r\n      default:\r\n        return (\r\n          <InterviewInstructions onNext={() => setCurrentStep(\"questions\")} />\r\n        );\r\n    }\r\n  };\r\n\r\n  return <div>{renderCurrentComponent()}</div>;\r\n};\r\n\r\nexport default Interview;\r\n"], "names": [], "mappings": ";;;;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AARA;;;;;;;;;AAkBA,MAAM,YAAY;;IAChB,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAC9D,MAAM,YAAY,CAAA,GAAA,gHAAA,CAAA,eAAY,AAAD;IAE7B,MAAM,yBAAyB;QAC7B,OAAQ;YACN,KAAK;gBACH,qBACE,6LAAC,oJAAA,CAAA,UAAqB;oBAAC,QAAQ,IAAM,eAAe;;;;;;YAExD,KAAK;gBACH,qBAAO,6LAAC,4IAAA,CAAA,UAAa;oBAAC,QAAQ,IAAM,eAAe;;;;;;YACrD,KAAK;gBACH,qBACE,6LAAC,kJAAA,CAAA,UAAmB;oBAClB,YAAY,IAAM,eAAe;oBACjC,WAAW,aAAa;;;;;;YAG9B,KAAK;gBACH,qBACE,6LAAC,iJAAA,CAAA,UAAkB;oBACjB,QAAQ,IAAM,eAAe;;;;;;YAGnC,KAAK;gBACH,qBAAO,6LAAC,8IAAA,CAAA,UAAe;oBAAC,QAAQ,IAAM,eAAe;;;;;;YAEvD,KAAK;gBACH,qBAAO,6LAAC,uIAAA,CAAA,UAAQ;;;;;YAClB;gBACE,qBACE,6LAAC,oJAAA,CAAA,UAAqB;oBAAC,QAAQ,IAAM,eAAe;;;;;;QAE1D;IACF;IAEA,qBAAO,6LAAC;kBAAK;;;;;;AACf;GAtCM;KAAA;uCAwCS", "debugId": null}}]}